import { FulfillmentMethod } from '@/components/Fulfillment/FulfillmentMethodButton'
import { transformPriceAdjustmentData } from '@/serializer/priceAdjustment.serializer'
import { Customers } from '@/types/module/addCustomersModule'
import { TextSale } from '@/types/module/commonModule'
import { Fulfillment } from '@/types/module/fulfillmentModule'
import {
  CleaningService,
  CleaningServiceForBackendPayload,
} from '@/types/module/itemModule'
import { PriceAdjustment } from '@/types/module/priceAdjustmentModule'
import { PrintOptions } from '@/types/module/printersModule'
import { Store } from '@/types/module/storeModule'
import { TeamMember } from '@/types/module/teamMembersModule'
import { TicketStatusForFilter } from '@/types/module/ticketFiltersModule'
import {
  BackendCurrentTicketData,
  BackendEditStateLineItem,
  BackendLineItem,
  BillingEvent,
  CRUD_OPERATION_TYPE,
  CurrentTicketData,
  EDIT_TICKET_ACTION_TYPE,
  EditStateLineItem,
  EditStateSubline,
  EditTicketData,
  excludedFieldMapping,
  GroupedItemsPrintTicket,
  Item,
  LineItem,
  Memo,
  PriceAdjustmentData,
  SelectedCustomerDetail,
  SelectedIndexes,
  STMConfigType,
  Subline,
  SublineType,
  TaxConfiguration,
  TicketData,
  TicketDataBE,
  TicketItemsBE,
  TicketLayoutSetting,
  TicketPrintTransFormDataType,
} from '@/types/module/ticketNdSalesModule'
import {
  SettingsBases,
  SettingsSubTickets,
  TicketSettings,
} from '@/types/module/ticketSettingsModule'
import { Variant } from '@/types/module/variantsModule'
import { DATE_FORMAT_MM_DD_YY_HH_MM_A_WITHOUT_COMMA } from '@/utils/constant'
import {
  convertToTimeZone,
  formatDateTimeToISO,
  getWeekdayName,
} from '@/utils/date'
import {
  convertToCurrency,
  getCleaningItemPieces,
  getTexRateValue,
  isP2AndP3ItemTypesExist,
  isValueChanged,
  transformWeightedItemValue,
} from '@/utils/functions'
import { formatTicketNumber } from '@/utils/ticket'
import { translation } from '@/utils/translation'
import { isAfter, isEqual } from 'date-fns'
import { formatInTimeZone } from 'date-fns-tz'
import { v4 as uuidv4 } from 'uuid'

const itemsTransformData = (itemBE: TicketItemsBE): Item => {
  return {
    id: itemBE.itemId,
    quantity: itemBE.quantity,
    description: itemBE.name,
    unitPrice: itemBE.price,
    subtotal: itemBE.subtotal,
    variants: [],
    subLines: [],
    tax: itemBE.tax,
    total: itemBE.subtotal,
    sublines: itemBE.sublines,
    itemType: itemBE.itemType,
    cleaningService: itemBE?.cleaningService || '',
    pieces: itemBE?.pieces,
    minLbs: itemBE?.minLbs,
    pricePerLb: itemBE?.pricePerLb,
  }
}

export const ticketTransformData = (ticketBE: TicketDataBE): TicketData => {
  const openDate =
    ticketBE.createTimestamp && new Date(ticketBE.createTimestamp)
  const closeDate =
    ticketBE?.closeTimestamp && new Date(ticketBE?.closeTimestamp)
  const dueDate =
    ticketBE?.fulfillment?.date &&
    (formatDateTimeToISO(
      ticketBE?.fulfillment?.date,
      ticketBE?.fulfillment?.time
    ) as Date)
  const items = ticketBE.lineItems
    ? ticketBE.lineItems.map((item: TicketItemsBE) => itemsTransformData(item))
    : []
  const priceAdjustments = ticketBE.priceAdjustments
    ? ticketBE.priceAdjustments.map((priceAdjustment) =>
        transformPriceAdjustmentData(priceAdjustment)
      )
    : []

  return {
    number: ticketBE.ticketId,
    items,
    subtotal: ticketBE.subtotal,
    storeCustomerId: ticketBE.storeCustomerId,
    tax: ticketBE.taxTotal,
    total: ticketBE.total,
    ticketStatus: ticketBE.ticketStatus,
    paymentStatus: ticketBE.paidStatus,
    closeDate: closeDate || null,
    openDate: openDate || null,
    dueDate: dueDate || null,
    totalMemo: ticketBE.totalMemo,
    totalDiscount: ticketBE.totalDiscount,
    totalSurcharge: ticketBE.totalSurcharge,
    priceAdjustments,
    memos: ticketBE.memos,
    billingEvents: ticketBE.billingEvents,
    totalAmountPaid: ticketBE.totalAmountPaid,
    createTeamMemberName: ticketBE.createTeamMemberName,
    fulfillment: ticketBE.fulfillment,
    balance: ticketBE.balance,
    flagged: ticketBE?.flagged,
    notes: ticketBE?.notes,
    taxConfiguration: ticketBE?.taxConfiguration,
    rackId: ticketBE?.rackId,
  }
}

function filterItemData(itemData: LineItem): LineItem {
  const filteredData: Partial<LineItem> = { ...itemData }
  const { itemType } = itemData

  const fieldsToRemove = excludedFieldMapping[itemType] || []
  fieldsToRemove.forEach((field) => delete filteredData[field])

  return filteredData as LineItem
}

const handleDeleteLineItem = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null
): EditTicketData => {
  if (selectedIndexes?.lineItemIndex === undefined) return editTicketData

  const { lineItemIndex } = selectedIndexes

  if (lineItemIndex < 0 || lineItemIndex >= editTicketData.lineItems.length) {
    return editTicketData
  }

  const deletedLineItem = editTicketData.lineItems[lineItemIndex]
  const updatedEditLineItems = editTicketData.lineItems.filter(
    (_, index) => index !== lineItemIndex
  )

  if (deletedLineItem?.isPreSet) {
    const modifiedLineItem: EditStateLineItem = {
      ...deletedLineItem,
      action: CRUD_OPERATION_TYPE.DELETE,
    }
    updatedEditLineItems.push(modifiedLineItem)
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const handleAddLineItem = (
  editTicketData: EditTicketData,
  currentTicketData: CurrentTicketData,
  selectedIndexes: SelectedIndexes | null
): EditTicketData => {
  if (!currentTicketData.lineItems.length || selectedIndexes === null)
    return editTicketData

  const { lineItemIndex = currentTicketData.lineItems.length - 1 } =
    selectedIndexes

  const newLineItem = currentTicketData.lineItems[lineItemIndex]

  const updatedEditLineItems = [...editTicketData.lineItems]

  const modifiedLineItem: EditStateLineItem = {
    ...newLineItem,
    action: CRUD_OPERATION_TYPE.ADD,
  }

  updatedEditLineItems.splice(lineItemIndex, 0, modifiedLineItem)

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const handleEditLineItem = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (selectedIndexes?.lineItemIndex === undefined) return editTicketData

  const { lineItemIndex } = selectedIndexes

  if (lineItemIndex < 0 || lineItemIndex >= editTicketData.lineItems.length) {
    return editTicketData
  }

  const updatedCurrentLineItems = structuredClone(currentTicketData.lineItems)

  const updatedEditLineItems = structuredClone(editTicketData.lineItems)

  const originalLineItem = updatedCurrentLineItems[lineItemIndex]

  if (
    originalLineItem.isPreSet &&
    ((originalLineItem.originalData?.cleaningService &&
      originalLineItem.originalData?.cleaningService !==
        originalLineItem.cleaningService) ||
      originalLineItem.originalData?.quantity !== originalLineItem.quantity ||
      originalLineItem.originalData?.price !== originalLineItem.price)
  ) {
    const checkIsSublineUpdated =
      updatedCurrentLineItems[lineItemIndex].sublines?.length !==
      updatedEditLineItems[lineItemIndex].sublines?.length
    updatedEditLineItems[lineItemIndex] = {
      ...originalLineItem,
      ...(checkIsSublineUpdated
        ? { sublines: updatedEditLineItems[lineItemIndex].sublines }
        : {}),
      action: CRUD_OPERATION_TYPE.EDIT,
    }
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const handleAddSubline = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (
    selectedIndexes?.lineItemIndex === undefined ||
    selectedIndexes?.sublineIndex === undefined
  )
    return editTicketData

  const { lineItemIndex, sublineIndex } = selectedIndexes
  if (lineItemIndex < 0 || lineItemIndex >= currentTicketData.lineItems.length)
    return editTicketData
  if (
    sublineIndex < 0 ||
    sublineIndex >= currentTicketData.lineItems[lineItemIndex].sublines.length
  )
    return editTicketData

  const updatedCurrentLineItems = currentTicketData.lineItems.map(
    (lineItem) => ({
      ...lineItem,
      sublines: lineItem.sublines.map((subline) => ({ ...subline })),
    })
  )

  const newSubline =
    updatedCurrentLineItems[lineItemIndex].sublines[sublineIndex]
  const updatedEditLineItems = editTicketData.lineItems.map((lineItem) => ({
    ...lineItem,
    sublines: lineItem.sublines.map((subline) => ({ ...subline })),
  }))

  const modifiedSubline: EditStateSubline = {
    ...newSubline,
    action: CRUD_OPERATION_TYPE.ADD,
  }

  updatedEditLineItems[lineItemIndex].sublines.splice(
    sublineIndex,
    0,
    modifiedSubline
  )
  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

export const transformEditTicketWithOriginalData = (
  currentTicketData: CurrentTicketData,
  action: EDIT_TICKET_ACTION_TYPE | null,
  selectedIndexes: SelectedIndexes | null,
  editTicketData: EditTicketData | null
): EditTicketData => {
  if (editTicketData === null) return currentTicketData
  const updatedEditTicketData = structuredClone(editTicketData)
  const updatedSelectedIndexes = structuredClone(selectedIndexes)
  const updatedCurrentTicketData = structuredClone(currentTicketData)
  switch (action) {
    case EDIT_TICKET_ACTION_TYPE.DELETE_LINE_ITEM:
      return handleDeleteLineItem(updatedEditTicketData, updatedSelectedIndexes)

    case EDIT_TICKET_ACTION_TYPE.ADD_LINE_ITEM:
      return handleAddLineItem(
        updatedEditTicketData,
        updatedCurrentTicketData,
        updatedSelectedIndexes
      )
    case EDIT_TICKET_ACTION_TYPE.EDIT_LINE_ITEM:
      return handleEditLineItem(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    case EDIT_TICKET_ACTION_TYPE.ADD_SUBLINE:
      return handleAddSubline(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    case EDIT_TICKET_ACTION_TYPE.EDIT_SUBLINE:
      return handleEditSubline(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    case EDIT_TICKET_ACTION_TYPE.EDIT_QUANTITY:
      return handleEditQuantity(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    case EDIT_TICKET_ACTION_TYPE.DELETE_SUBLINE:
      return handleDeleteSubline(updatedEditTicketData, updatedSelectedIndexes)

    case EDIT_TICKET_ACTION_TYPE.CLEAR_EMPTY_SUBLINE:
      return handleEmptySubline(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    case EDIT_TICKET_ACTION_TYPE.REMOVE_FIRST_THEN_ADD_SUBLINE:
      return handleRemoveFirstAndThenAddSubline(
        updatedEditTicketData,
        updatedSelectedIndexes,
        updatedCurrentTicketData
      )

    default:
      return updatedEditTicketData
  }
}

const handleDeleteSubline = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null
): EditTicketData => {
  if (
    selectedIndexes?.lineItemIndex === undefined ||
    selectedIndexes?.sublineIndex === undefined
  )
    return editTicketData

  const { lineItemIndex, sublineIndex } = selectedIndexes
  if (lineItemIndex < 0 || lineItemIndex >= editTicketData.lineItems.length)
    return editTicketData
  if (
    sublineIndex < 0 ||
    sublineIndex >= editTicketData.lineItems[lineItemIndex].sublines.length
  )
    return editTicketData

  const deletedSubline =
    editTicketData.lineItems[lineItemIndex].sublines[sublineIndex]
  const updatedEditLineItems = [...editTicketData.lineItems]

  updatedEditLineItems[lineItemIndex].sublines = updatedEditLineItems[
    lineItemIndex
  ].sublines.filter((_, index) => index !== sublineIndex)

  if (deletedSubline.isPreSet) {
    updatedEditLineItems[lineItemIndex].sublines.push({
      ...deletedSubline,
      ...(deletedSubline.originalData?.comment
        ? { comment: deletedSubline.originalData.comment }
        : {}),
      ...(deletedSubline.originalData?.addOnName
        ? { addOnName: deletedSubline.originalData.addOnName }
        : {}),
      action: CRUD_OPERATION_TYPE.DELETE,
    })
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const handleEditSubline = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (
    selectedIndexes?.lineItemIndex === undefined ||
    selectedIndexes?.sublineIndex === undefined
  )
    return editTicketData

  const { lineItemIndex, sublineIndex } = selectedIndexes
  if (lineItemIndex < 0 || lineItemIndex >= editTicketData.lineItems.length)
    return editTicketData
  if (
    sublineIndex < 0 ||
    sublineIndex >= editTicketData.lineItems[lineItemIndex].sublines.length
  )
    return editTicketData

  const updatedEditLineItems = editTicketData.lineItems.map((lineItem) => ({
    ...lineItem,
    sublines: lineItem.sublines.map((subline) => ({ ...subline })),
  }))

  const originalSubline =
    currentTicketData.lineItems[lineItemIndex].sublines[sublineIndex]

  if (
    originalSubline.isPreSet &&
    ((originalSubline.originalData?.comment &&
      originalSubline.originalData?.comment !== originalSubline.comment) ||
      originalSubline.originalData?.quantity !== originalSubline.quantity ||
      originalSubline.originalData?.price !== originalSubline.price)
  ) {
    updatedEditLineItems[lineItemIndex].sublines[sublineIndex] = {
      ...updatedEditLineItems[lineItemIndex].sublines[sublineIndex],
      ...originalSubline,
      action: CRUD_OPERATION_TYPE.EDIT,
    }
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const handleEditQuantity = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (selectedIndexes?.lineItemIndex === undefined) return editTicketData

  const { lineItemIndex } = selectedIndexes

  const updatedCurrentTicketData = [...currentTicketData.lineItems]

  const updatedEditLineItems = [...editTicketData.lineItems]

  const updatedSublines: EditStateSubline[] = updatedEditLineItems[
    lineItemIndex
  ].sublines?.map((value: Subline, index) => {
    return {
      ...value,
      quantity:
        updatedEditLineItems[lineItemIndex].sublines[index].action ===
        CRUD_OPERATION_TYPE.DELETE
          ? value.quantity
          : (updatedCurrentTicketData[lineItemIndex].quantity as number),
      ...(updatedEditLineItems[lineItemIndex].sublines[index].isPreSet &&
      updatedEditLineItems[lineItemIndex].sublines[index].action !==
        CRUD_OPERATION_TYPE.DELETE
        ? { action: CRUD_OPERATION_TYPE.EDIT }
        : {}),
    }
  })

  updatedEditLineItems[lineItemIndex] = {
    ...updatedEditLineItems[lineItemIndex],
    quantity: updatedCurrentTicketData[lineItemIndex].quantity,
    sublines: updatedSublines,
    ...(updatedEditLineItems[lineItemIndex].isPreSet
      ? { action: CRUD_OPERATION_TYPE.EDIT }
      : {}),
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

const checkIsCrudActionExist = (
  updatedSubline: EditStateSubline
): CRUD_OPERATION_TYPE | undefined => {
  return updatedSubline.action !== undefined &&
    [
      CRUD_OPERATION_TYPE.ADD,
      CRUD_OPERATION_TYPE.EDIT,
      CRUD_OPERATION_TYPE.DELETE,
    ].includes(updatedSubline.action)
    ? updatedSubline.action
    : undefined
}

const checkIsCrudActionExistInSublines = (
  updatedSubline: EditStateSubline[],
  lineItem: EditStateLineItem
): boolean => {
  const { cleaningService, price, quantity } = lineItem.originalData || {}
  if (
    quantity !== lineItem.quantity ||
    price !== lineItem.price ||
    (cleaningService && cleaningService !== lineItem.cleaningService)
  ) {
    return true
  }
  return updatedSubline.some((subline) => {
    return (
      subline.action !== undefined &&
      [
        CRUD_OPERATION_TYPE.ADD,
        CRUD_OPERATION_TYPE.EDIT,
        CRUD_OPERATION_TYPE.DELETE,
      ].includes(subline.action)
    )
  })
}

export const handleEmptySubline = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (
    selectedIndexes?.lineItemIndex === undefined ||
    selectedIndexes?.sublinesIndex === undefined
  )
    return editTicketData

  const { lineItemIndex, sublinesIndex } = selectedIndexes

  const updatedCurrentLineItems = structuredClone(currentTicketData.lineItems)

  const currentTicketSubline = [
    ...updatedCurrentLineItems[lineItemIndex].sublines,
  ]

  let updatedEditLineItems = structuredClone(editTicketData.lineItems)

  const editTicketSubline = updatedEditLineItems[lineItemIndex].sublines.filter(
    (_, index) => !sublinesIndex.includes(index)
  )

  const updatedSubline: EditStateSubline[] = editTicketSubline?.map(
    (value, index) => {
      if (currentTicketSubline[index]) {
        return {
          ...currentTicketSubline[index],
          action: value?.action,
        }
      }
      return {
        ...value,
        action: CRUD_OPERATION_TYPE.DELETE,
      }
    }
  )

  updatedCurrentLineItems[lineItemIndex].sublines = updatedSubline?.map(
    (value, index) => {
      if (
        value.action !== CRUD_OPERATION_TYPE.DELETE &&
        value.isPreSet &&
        value.originalData &&
        (value.originalData.price !== value.price ||
          value.originalData.quantity !== value.quantity ||
          (value.originalData.isAlterationMatched
            ? isValueChanged(value.originalData.comment, value.comment)
            : value.originalData.addOnName === undefined &&
              isValueChanged(value.originalData.comment, value.comment)) ||
          (value.addOnName && value.originalData.addOnName !== value.addOnName))
      ) {
        return {
          ...value,
          action: CRUD_OPERATION_TYPE.EDIT,
        }
      } else {
        return {
          ...value,
          action: checkIsCrudActionExist(updatedSubline[index]),
        }
      }
    }
  )

  updatedEditLineItems = updatedEditLineItems.map((value, index) => {
    const updatedLineItems = structuredClone(
      index === lineItemIndex
        ? { ...updatedCurrentLineItems[lineItemIndex], action: value.action }
        : { ...value }
    )
    let action = value?.action
    if (updatedLineItems.action === undefined) {
      action = checkIsCrudActionExistInSublines(
        updatedLineItems.sublines,
        updatedLineItems
      )
        ? CRUD_OPERATION_TYPE.EDIT
        : undefined
      return { ...updatedLineItems, action }
    }
    return {
      ...updatedLineItems,
    }
  })
  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

export const handleRemoveFirstAndThenAddSubline = (
  editTicketData: EditTicketData,
  selectedIndexes: SelectedIndexes | null,
  currentTicketData: CurrentTicketData
): EditTicketData => {
  if (
    selectedIndexes?.lineItemIndex === undefined ||
    selectedIndexes?.sublineIndex === undefined ||
    selectedIndexes?.sublinesIndex === undefined ||
    selectedIndexes?.sublinesIndex?.length === 0
  )
    return editTicketData

  const { lineItemIndex, sublinesIndex } = selectedIndexes

  const updatedCurrentLineItems = currentTicketData.lineItems.map(
    (lineItem) => ({
      ...lineItem,
      sublines: lineItem.sublines.map((subline) => ({ ...subline })),
    })
  )

  const currentTicketSubline = [
    ...updatedCurrentLineItems[lineItemIndex].sublines,
  ]

  const updatedEditLineItems = editTicketData.lineItems.map((lineItem) => ({
    ...lineItem,
    sublines: lineItem.sublines.map((subline) => ({ ...subline })),
  }))

  const editTicketSubline = updatedEditLineItems[lineItemIndex].sublines.filter(
    (_, index) => !sublinesIndex.includes(index)
  )

  const deletedSubline = updatedEditLineItems[lineItemIndex].sublines
    .filter((val, index) => val.isPreSet && sublinesIndex.includes(index))
    ?.map((value) => {
      return {
        ...value,
        action: CRUD_OPERATION_TYPE.DELETE,
      }
    })

  updatedEditLineItems[lineItemIndex].sublines = [...editTicketSubline]

  const newlyAddedSubline =
    currentTicketSubline[currentTicketSubline.length - 1]

  const modifyNewlyAddedSubline = {
    ...newlyAddedSubline,
    action: CRUD_OPERATION_TYPE.ADD,
  }

  updatedEditLineItems[lineItemIndex].sublines.splice(
    currentTicketSubline.length - 1,
    0,
    modifyNewlyAddedSubline
  )

  if (deletedSubline.length > 0) {
    updatedEditLineItems[lineItemIndex].sublines = [
      ...updatedEditLineItems[lineItemIndex].sublines,
      ...deletedSubline,
    ]
  }

  return {
    ...editTicketData,
    lineItems: updatedEditLineItems,
  }
}

export const transformCurrentTicketWithOriginalData = (
  currentTicketData: CurrentTicketData,
  variantsData?: Variant[] | null
): CurrentTicketData => {
  const { lineItems, ...rest } = currentTicketData
  const updatedLineItems = lineItems?.map((value, lineItemIndex) => {
    const sublines = value?.sublines?.map((val, sublineIndex) => {
      const isAlterationMatched =
        variantsData?.some(
          (data) =>
            data.variantId === val.variantId &&
            data.name === translation.ALTERATIONS
        ) || false
      return {
        ...val,
        isPreSet: true,
        deletedIndex: sublineIndex,
        originalData: {
          price: val.price as number,
          comment: val?.comment,
          quantity: val.quantity as number,
          addOnName: val?.addOnName,
          isAlterationMatched,
        },
      }
    })

    return {
      ...value,
      sublines,
      isPreSet: true,
      deletedIndex: lineItemIndex,
      originalData: {
        price: value.price as number,
        quantity: value.quantity as number,
        ...(value.itemType === TextSale.CLEANING
          ? { cleaningService: value?.cleaningService }
          : {}),
      },
    }
  })

  return {
    ...rest,
    lineItems: updatedLineItems,
  }
}

export const transformCurrentTicket = (
  currentTicketData: CurrentTicketData,
  storeCustomerId: number
): BackendCurrentTicketData => {
  if (
    Array.isArray(currentTicketData.priceAdjustments) &&
    currentTicketData?.priceAdjustments?.length === 0
  ) {
    delete currentTicketData.priceAdjustments
  } else {
    currentTicketData.priceAdjustments =
      currentTicketData.priceAdjustments?.map((value) => {
        if (value.priceAdjustmentId === 0) {
          // eslint-disable-next-line
          const { priceAdjustmentId, ...rest } = value
          return rest
        }
        return value
      })
  }

  if (
    Array.isArray(currentTicketData.memos) &&
    currentTicketData?.memos?.length === 0
  ) {
    delete currentTicketData.memos
  }
  currentTicketData.taxConfiguration.calculationBase =
    currentTicketData.taxConfiguration.calculationBase === 'PRE_TAX'
      ? 'NET_SALES'
      : 'GROSS_SALES'

  const updatedLineItems = currentTicketData?.lineItems?.map((value) => {
    const sublines = value?.sublines?.map((val) => {
      // eslint-disable-next-line
      const { uniqueId, ...rest } = val
      return {
        ...rest,
        price: val?.price ?? 0,
        quantity: value.itemType === TextSale.WEIGHTED ? 1 : val.quantity,
      }
    })
    // eslint-disable-next-line
    const { uniqueId, itemIndex, isQuantityModalOpen, isSelected, ...rest } =
      value
    const updatedItemData: LineItem = filterItemData(rest as LineItem)
    return {
      ...updatedItemData,
      sublines,
    } as LineItem
  })
  return {
    ...currentTicketData,
    lineItems: updatedLineItems as BackendLineItem[],
    notes: currentTicketData?.notes,
    storeCustomerId,
    fulfillment: currentTicketData?.fulfillment || {},
    flagged: currentTicketData?.flagged || false,
  }
}

export function transformModalPrintTicket(
  copies: number,
  option: PrintOptions,
  variantsList: Variant[],
  customersList: Customers[] | null,
  ticketSettingsData: TicketSettings | null,
  store: Store,
  ticketData: TicketDataBE | null,
  selectedTeamMember: TeamMember,
  isStmEditor: boolean
) {
  const isStoreInformation =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.STORE_INFORMATION
    )?.enabled

  const isTicketBarcode =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.TICKET_BARCODE
    )?.enabled

  const isAddOnPrices =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.ITEM_ADD_ON_PRICES
    )?.enabled

  const modifiedTicketData = ticketData
  if (!isAddOnPrices && modifiedTicketData !== null) {
    modifiedTicketData.lineItems = modifiedTicketData?.lineItems.map(
      (value) => {
        let totalAmount = value?.price
        value?.sublines?.forEach((subline) => {
          if (subline.price !== null) {
            totalAmount += subline.price
          }
        })
        value.price = totalAmount
        return value
      }
    )
  }
  const printData = {
    variantsList,
    customersList,
    ticketSettingsData,
    store,
    ticketData: modifiedTicketData,
    isStoreInformation,
    isTicketBarcode,
    isAddOnPrices,
    isStmEditor,
    copies,
    option,
    selectedTeamMember,
  }
  return printData
}

export function transformPrintTicket(
  copies: number,
  option: PrintOptions,
  variantsList: Variant[],
  customersList: Customers[] | null,
  ticketSettingsData: TicketSettings | null,
  store: Store,
  ticketData: TicketData,
  selectedTeamMember: TeamMember,
  isStmEditor: boolean
) {
  const isStoreInformation =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.STORE_INFORMATION
    )?.enabled

  const isTicketBarcode =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.TICKET_BARCODE
    )?.enabled

  const isAddOnPrices =
    Array.isArray(ticketSettingsData?.printSections) &&
    ticketSettingsData?.printSections?.find(
      (value) => value.name === TicketLayoutSetting.ITEM_ADD_ON_PRICES
    )?.enabled

  const modifiedTicketData = {
    ...ticketData,
    items: ticketData?.items?.map((item) => ({
      ...item,
      sublines: item?.sublines ? [...item?.sublines] : [],
    })),
  }

  if (!isAddOnPrices && modifiedTicketData !== null) {
    modifiedTicketData.items = modifiedTicketData.items.map((value) => {
      let totalAmount = value.unitPrice
      if (
        value?.cleaningService !== '' &&
        CleaningService?.[
          value?.cleaningService as keyof typeof CleaningService
        ]
      ) {
        value?.sublines?.forEach((subline) => {
          if (subline.price !== null) {
            totalAmount += subline.price
          }
        })
      }
      return { ...value, unitPrice: totalAmount }
    })
  }

  const printData = {
    variantsList,
    customersList,
    ticketSettingsData,
    store,
    ticketData: {
      ...modifiedTicketData,
      closeTimestamp: modifiedTicketData.closeDate,
      lineItems: modifiedTicketData?.items?.map((item) => ({
        ...item,
        name: item?.description,
        price: item?.unitPrice,
        itemId: item?.id,
      })),
      paidStatus: modifiedTicketData?.paymentStatus,
      taxTotal: modifiedTicketData?.tax,
      ticketId: ticketData?.number,
    },
    isStoreInformation,
    isTicketBarcode,
    isAddOnPrices,
    selectedTeamMember,
    isStmEditor,
    copies,
    option,
  }
  return printData
}

export const generateSTMDataConfig = (
  data: TicketPrintTransFormDataType
): STMConfigType => {
  // Number of copies to print
  const numberOfPrintCopies = data?.copies

  // Sub-ticket settings
  const isDryCleanEnabled =
    Array.isArray(data?.ticketSettingsData?.subTicketsEnabled) &&
    data?.ticketSettingsData?.subTicketsEnabled?.some(
      (subTicket) =>
        subTicket.name === SettingsSubTickets.DRY_CLEAN && subTicket.enabled
    )

  const isWetCleanEnabled =
    Array.isArray(data?.ticketSettingsData?.subTicketsEnabled) &&
    data?.ticketSettingsData?.subTicketsEnabled?.some(
      (subTicket) =>
        subTicket.name === SettingsSubTickets.WET_CLEAN && subTicket.enabled
    )

  const isCleaningWeightEnabled =
    Array.isArray(data?.ticketSettingsData?.subTicketsEnabled) &&
    data?.ticketSettingsData?.subTicketsEnabled?.some(
      (subTicket) =>
        subTicket.name === SettingsSubTickets.CLEANING_WEIGHT &&
        subTicket.enabled
    )

  const isAlterationEnabled =
    Array.isArray(data?.ticketSettingsData?.subTicketsEnabled) &&
    data?.ticketSettingsData?.subTicketsEnabled?.some(
      (subTicket) =>
        subTicket.name === SettingsSubTickets.ALTERATIONS && subTicket.enabled
    )

  // Ticket layout settings
  const isRushEnabled =
    Array.isArray(data?.ticketSettingsData?.printSections) &&
    data?.ticketSettingsData?.printSections?.some(
      (section) => section.name === SettingsBases.RUSH && section.enabled
    )

  const isCustomerAddressEnabled =
    Array.isArray(data?.ticketSettingsData?.printSections) &&
    data?.ticketSettingsData?.printSections?.some(
      (section) =>
        section.name === SettingsBases.CUSTOMER_ADDRESS && section.enabled
    )
  const isCustomerPhoneNumberEnabled =
    Array.isArray(data?.ticketSettingsData?.printSections) &&
    data?.ticketSettingsData?.printSections?.some(
      (section) =>
        section.name === SettingsBases.CUSTOMER_PHONE_NUMBER && section.enabled
    )
  const isMessageOnTicketEnabled =
    Array.isArray(data?.ticketSettingsData?.printSections) &&
    data?.ticketSettingsData?.printSections?.some(
      (section) =>
        section.name === SettingsBases.MESSAGE_ON_TICKET && section.enabled
    )

  // Message on ticket
  const ticketMessage = data?.ticketSettingsData?.messageOnTicket

  const closeDate =
    data?.ticketData &&
    data?.ticketData?.ticketStatus === TicketStatusForFilter.CLOSED
      ? data?.ticketData?.closeTimestamp
      : data?.ticketData?.openDate || data?.ticketData?.createTimestamp

  const adjustedDate = closeDate
    ? formatInTimeZone(
        closeDate,
        data.store?.timeZone || '',
        DATE_FORMAT_MM_DD_YY_HH_MM_A_WITHOUT_COMMA
      )
    : null

  const lineItems = data?.ticketData?.lineItems || []

  const modifiedAlterationLineItems = modifiedAlterationItems(
    data.variantsList as Variant[],
    lineItems as TicketItemsBE[]
  )

  const selectedCustomer = data.customersList?.find(
    (customer) => customer.storeCustomerId === data?.ticketData?.storeCustomerId
  )

  const customer = {
    name: `${selectedCustomer?.firstName} ${selectedCustomer?.lastName}`,
    address: selectedCustomer?.addressLine,
    phoneNumber: selectedCustomer?.phoneNumber,
  }

  const fulfillmentDetail =
    data.ticketData?.fulfillment?.method !== FulfillmentMethod.NONE
      ? data.ticketData?.fulfillment
      : null

  const fulfillmentDate =
    fulfillmentDetail !== null &&
    data.ticketData?.fulfillment?.method !== FulfillmentMethod.NONE
      ? (formatDateTimeToISO(
          data.ticketData?.fulfillment?.date as string,
          data.ticketData?.fulfillment?.time as number
        ) as Date)
      : null

  const fulfillmentDay =
    fulfillmentDetail !== null &&
    data.ticketData?.fulfillment?.method !== FulfillmentMethod.NONE
      ? getWeekdayName(data.ticketData?.fulfillment?.date as string)
      : null

  const numberOfPieces = getCleaningItemPieces(
    lineItems as LineItem[],
    TextSale.CLEANING,
    true
  )

  const groupedTransformData = (
    modifiedAlterationLineItems: TicketItemsBE[] | undefined
  ) => {
    const itemOrder: string[] = []
    const groupedData = modifiedAlterationLineItems?.reduce(
      (group: Record<string, GroupedItemsPrintTicket>, item: TicketItemsBE) => {
        const groupKey = item.name
        if (!itemOrder.includes(groupKey)) {
          itemOrder.push(groupKey)
        }
        if (!group[groupKey]) {
          group[groupKey] = {
            name: '',
            items: [],
            totalQuantity: 0,
          }
        }
        group[groupKey].items.push(item)
        if (item?.quantity) {
          group[groupKey].totalQuantity +=
            item?.itemType === TextSale.WEIGHTED?.toString().toLocaleUpperCase()
              ? 1
              : item?.quantity
        }

        return group
      },
      {}
    )

    const orderGroupedItemsData = itemOrder.map((value: string) => {
      return {
        name: value,
        items: groupedData?.[value].items,
        totalQuantity: groupedData?.[value].totalQuantity,
      }
    })

    return orderGroupedItemsData
  }

  const wetClean = getModifiedData(data?.ticketData, 'CLEANING', 'WET_CLEAN')
  const dryClean = getModifiedData(data?.ticketData, 'CLEANING', 'DRY_CLEAN')
  const weighted = getModifiedData(data?.ticketData, 'WEIGHTED')
  const wetCleanGroupedItem = groupedTransformData(
    getModifiedData(data?.ticketData, 'CLEANING', 'WET_CLEAN')?.lineItems
  )
  const dryCleanGroupedItem = groupedTransformData(
    getModifiedData(data.ticketData, 'CLEANING', 'DRY_CLEAN')?.lineItems
  )

  const alterationGroupedItem = groupedTransformData(
    modifiedAlterationLineItems
      ?.filter((item) =>
        Object.values(CleaningServiceForBackendPayload).includes(
          item.cleaningService as CleaningServiceForBackendPayload
        )
      )
      .filter(
        (item) => item?.sublines?.some((subline) => subline.isAlterationMatched) // Keep only items with matched sublines
      )
      .map((item) => {
        const matchedSublines = item?.sublines?.filter(
          (subline) => subline?.isAlterationMatched
        )
        return {
          ...item,
          sublines: matchedSublines, // Return only matched sublines
        }
      })
  )

  const weightedGroupedItem = groupedTransformData(
    getModifiedData(data.ticketData, 'WEIGHTED')?.lineItems
  )

  const wetCleanGroupedItemPieces = getCleaningItemPieces(
    wetCleanGroupedItem?.flatMap((group) => group.items) as LineItem[],
    TextSale.CLEANING,
    true
  )

  const alterationGroupedItemPieces = getCleaningItemPieces(
    alterationGroupedItem?.flatMap((group) => group.items) as LineItem[],
    TextSale.CLEANING,
    true
  )

  const dryCleanGroupedItemPieces = getCleaningItemPieces(
    dryCleanGroupedItem?.flatMap((group) => group.items) as LineItem[],
    TextSale.CLEANING,
    true
  )

  // Construct modified data object
  const modifiedData = {
    option: data.option,
    ticketNumber: formatTicketNumber(
      data?.ticketData?.ticketId as number,
      false
    ),
    notes: data?.ticketData?.notes as string,
    adjustedDate,
    customer: customer as SelectedCustomerDetail,
    fulfillmentDay,
    isRushEnabled,
    isStoreInfoEnabled: data.isStoreInformation,
    isCustomerAddressEnabled,
    isCustomerPhoneNumberEnabled,
    isTicketBarcodeEnabled: data.isTicketBarcode,
    isAddOnPricesEnabled: data.isAddOnPrices,
    isMessageOnTicketEnabled,
    ticketMessage,
    lineItems: lineItems as LineItem[],
    orderGroupedItemsData: groupedTransformData(modifiedAlterationLineItems),
    storeCopy: {
      numberOfPrintCopies,
    },
    ticketSummary: {
      subtotal: convertToCurrency(
        data?.ticketData?.subtotal ? data?.ticketData?.subtotal.toString() : '0'
      ),
      tax: convertToCurrency(data?.ticketData?.taxTotal?.toString() || '0'),
      total: convertToCurrency(
        data?.ticketData?.total ? data?.ticketData?.total?.toString() : '0'
      ),
      OutstandingBalance: convertToCurrency(
        data?.ticketData?.balance?.toString() || '0'
      ),
      ...(data?.ticketData?.totalSurcharge &&
      Number(data?.ticketData?.totalSurcharge) > 0
        ? {
            totalSurcharge: convertToCurrency(
              data?.ticketData?.totalSurcharge?.toString()
            ),
          }
        : {}),
      ...(data?.ticketData?.totalDiscount &&
      Number(data?.ticketData?.totalDiscount) < 0
        ? {
            totalDiscount: convertToCurrency(
              data?.ticketData?.totalDiscount?.toString()
            ),
          }
        : {}),
    },
    storeData: {
      teamMemberName: data?.ticketData?.createTeamMemberName as string,
      isPaymentReceipt: false,
    },
    store: data.store,
    paymentDetails: {
      billingEvents: data?.ticketData?.billingEvents as BillingEvent[],
      totalAmountPaid: data?.ticketData?.totalAmountPaid as number,
    },
    fulfillmentDetail: fulfillmentDetail as Fulfillment | null,
    fulfillmentDate,
    numberOfPieces,
    subTicket: {
      isDryCleanEnabled,
      isWetCleanEnabled,
      isCleaningWeightEnabled,
      isAlterationEnabled,
      modifiedAlterationLineItems,
      subTicketItems: {
        wetClean,
        dryClean,
        weighted,
        wetCleanGroupedItem,
        dryCleanGroupedItem,
        weightedGroupedItem,
        alterationGroupedItem,
        wetCleanGroupedItemPieces,
        dryCleanGroupedItemPieces,
        alterationGroupedItemPieces,
      },
    },
    selectedTeamMember: data?.selectedTeamMember,
  }

  return modifiedData as STMConfigType
}

export const modifiedAlterationItems = (
  variantsList: Variant[],
  lineItems: TicketItemsBE[]
) => {
  const alterationVariant =
    variantsList.find((variant) => variant.name === translation.ALTERATIONS) ||
    false

  if (alterationVariant) {
    const alterationVariantId = alterationVariant.variantId

    // Iterate through lineItems
    lineItems?.forEach((item) => {
      item?.sublines?.forEach((subline) => {
        // Check if the subline is a VARIANT and matches the Alterations variantId
        if (
          subline.type === 'VARIANT' &&
          subline.variantId === alterationVariantId
        ) {
          subline.isAlterationMatched = true // Add boolean value
        } else {
          subline.isAlterationMatched = false // Default value if not matched
        }
      })
    })

    // return lineItems
  }
  return lineItems
}

function getModifiedData(
  data: Partial<TicketDataBE> | null,
  itemType: string,
  cleaningService: string | null = null
) {
  // Filter lineItems based on itemType and optional cleaningService

  const matchedItems = data?.lineItems?.filter(
    (item: TicketItemsBE) =>
      item.itemType === itemType &&
      (cleaningService ? item.cleaningService === cleaningService : true)
  )

  // Create updated object with all raw_data and matched lineItems
  return {
    ...data, // Spread all data from raw_data
    lineItems: matchedItems, // Replace lineItems with matched items only
  }
}

export const getGroupedSublineData = (sublines: Subline[]) => {
  return sublines?.reduce(
    (acc, subline) => {
      const addOnName = transformWeightedItemValue({
        addOnName: subline?.addOnName as string,
      })
      const comment = subline?.comment as string
      const price = subline.price

      if (subline.isAlterationMatched) {
        // Add to detailed sublines
        acc.detailedSublines?.push(subline)
      } else if (addOnName || comment || subline.type === SublineType.CUSTOM) {
        // Append to grouped text and sum the price
        if (addOnName || comment) {
          acc.groupedText.push(addOnName || comment)
        }

        if (price) acc.groupedPrice += Number(price)
      }

      return acc
    },
    {
      groupedText: [] as string[],
      groupedPrice: 0,
      detailedSublines: [] as Subline[],
    }
  )
}

export const updatedTicketData = (
  currentTicketData: CurrentTicketData | null,
  editTicketData: EditTicketData | null
): EditTicketData | null => {
  if (currentTicketData === null || editTicketData === null) return null
  const { lineItems } = { ...editTicketData }
  const updatedLineItems = [...lineItems]?.map((lineItem) => {
    if (lineItem.action === CRUD_OPERATION_TYPE.EDIT) {
      const { sublines } = lineItem
      const updatedSublines = [...sublines]?.map((subline) => {
        if (subline.action === CRUD_OPERATION_TYPE.EDIT) {
          const { price, originalData, quantity, comment, addOnName } = subline
          const {
            price: originalPrice,
            quantity: originalQuantity,
            comment: originalComment,
            addOnName: originalAddOnName,
            isAlterationMatched: originalIsAlterationMatched,
          } = originalData || {}
          if (
            price !== originalPrice ||
            quantity !== originalQuantity ||
            (originalIsAlterationMatched
              ? isValueChanged(originalComment, comment)
              : originalAddOnName === undefined &&
                isValueChanged(originalComment, comment)) ||
            (originalAddOnName && originalAddOnName !== addOnName)
          ) {
            return subline
          } else {
            // eslint-disable-next-line
            const { action, ...rest } = subline
            return rest
          }
        } else {
          return subline
        }
      })
      if (checkIsCrudActionExistInSublines(updatedSublines, lineItem)) {
        return {
          ...lineItem,
          sublines: updatedSublines,
        }
      } else {
        // eslint-disable-next-line
        const { action, ...rest } = lineItem
        return rest
      }
    } else {
      return lineItem
    }
  })
  const currentItemsMap = new Map(
    currentTicketData.lineItems.map((item) => [item.uniqueId, item])
  )

  let updatedLineItemData: EditStateLineItem[] = []

  updatedLineItems.forEach((editItem: EditStateLineItem) => {
    const currentItem = currentItemsMap.get(editItem.uniqueId)

    if (!currentItem) {
      updatedLineItemData.push({
        ...editItem,
      })
    } else {
      let updatedSublines: EditStateSubline[] = editItem.sublines.map(
        (subline: EditStateSubline, index) => {
          const matchingEditSubline = currentItem?.sublines[index]
          /* eslint-disable */
          const {
            isAlterationMatched = undefined,
            isPreSet,
            originalData,
            variantId,
            uniqueId,
            ...rest
          } = matchingEditSubline || {}
          return matchingEditSubline
            ? ({
                ...rest,
                action: subline.action || undefined,
                variantId: variantId || null,
              } as EditStateSubline)
            : subline
        }
      )
      /* eslint-enable */
      const deletedSublinesData = updatedSublines
        ?.filter((s) => s.action === CRUD_OPERATION_TYPE.DELETE)
        ?.sort((a, b) => {
          if (a.deletedIndex !== undefined && b.deletedIndex !== undefined) {
            return a.deletedIndex - b.deletedIndex
          } else {
            return 0
          }
        })

      updatedSublines = updatedSublines?.filter(
        (value) => value.action !== CRUD_OPERATION_TYPE.DELETE
      )

      deletedSublinesData.forEach((value) => {
        if (value.deletedIndex !== undefined) {
          updatedSublines.splice(value.deletedIndex, 0, value)
        }
      })

      if (currentItem) {
        /* eslint-disable */
        const {
          uniqueId,
          itemIndex,
          isQuantityModalOpen,
          isSelected,
          originalData,
          isPreSet,
          ...rest
        } = currentItem || {}
        const remaining =
          editItem.action === CRUD_OPERATION_TYPE.ADD
            ? rest
            : (({ name, price, ...otherProps }) => otherProps)(rest)
        /* eslint-enable */
        const updatedItemData: LineItem = filterItemData(remaining as LineItem)
        updatedLineItemData.push({
          ...updatedItemData,
          sublines: updatedSublines,
          action: editItem.action || undefined,
        })
      }
    }
  })

  const deletedLinItemData = updatedLineItemData
    ?.filter(
      (value: EditStateLineItem) => value.action === CRUD_OPERATION_TYPE.DELETE
    )
    ?.sort((a, b) => {
      if (a.deletedIndex !== undefined && b.deletedIndex !== undefined) {
        return a.deletedIndex - b.deletedIndex
      } else {
        return 0
      }
    })
  updatedLineItemData = updatedLineItemData?.filter(
    (value) => value.action !== CRUD_OPERATION_TYPE.DELETE
  )
  deletedLinItemData.forEach((value) => {
    if (value.deletedIndex !== undefined) {
      updatedLineItemData.splice(value.deletedIndex, 0, value)
    }
  })
  updatedLineItemData = [
    ...updatedLineItemData?.filter(
      (value) => value.action !== CRUD_OPERATION_TYPE.ADD
    ),
    ...updatedLineItemData?.filter(
      (value) => value.action === CRUD_OPERATION_TYPE.ADD
    ),
  ]
  /* eslint-disable */
  const {
    uniqueId,
    isEdit,
    isPreSet,
    notes,
    flagged,
    originalData,
    storeCustomerId,
    memos,
    priceAdjustments,
    fulfillment,
    originalFulfillment,
    ...rest
  } = currentTicketData || {}
  /* eslint-enable */
  const hasFlaggedValueChanged =
    flagged !== currentTicketData?.originalData?.flagged

  const { method } = originalFulfillment || {}

  let fulfillmentDetails
  if (method !== FulfillmentMethod.NONE) {
    const isP2AndP3ItemExist = isP2AndP3ItemTypesExist(
      currentTicketData?.lineItems || []
    )
    if (isP2AndP3ItemExist) {
      fulfillmentDetails = fulfillment
    } else {
      fulfillmentDetails = {
        isEdit: true,
        method: FulfillmentMethod.NONE,
      }
    }
  } else {
    fulfillmentDetails = fulfillment
  }

  const hasFulfillmentValueChanged = fulfillmentDetails?.isEdit
  /* eslint-disable */
  const { isEdit: isFulfillmentEdit, ...restFulfillmentDetails } =
    fulfillmentDetails || {}
  /* eslint-enable */
  return {
    ...rest,
    ...(isValueChanged(currentTicketData?.originalData?.notes, notes)
      ? { notes: notes }
      : {}),
    ...(currentTicketData?.priceAdjustments &&
    currentTicketData?.priceAdjustments?.length > 0
      ? { priceAdjustments }
      : {}),
    ...(currentTicketData?.memos && currentTicketData?.memos?.length > 0
      ? { memos }
      : {}),
    ...(hasFlaggedValueChanged ? { flagged } : {}),
    ...(hasFulfillmentValueChanged
      ? { fulfillment: restFulfillmentDetails as Fulfillment }
      : {}),
    lineItems: updatedLineItemData,
  }
}

export const updatedTicketPayloadData = (
  editTicketData: EditTicketData
): Partial<EditStateLineItem>[] => {
  const updatedEditLineItemsData: Partial<BackendEditStateLineItem>[] = []

  editTicketData.lineItems.forEach((lineItem: EditStateLineItem) => {
    const updatedLineItem: Partial<BackendEditStateLineItem> = {
      itemId: lineItem.itemId,
    }

    if (lineItem.action === CRUD_OPERATION_TYPE.DELETE) {
      updatedLineItem.action = CRUD_OPERATION_TYPE.DELETE
    } else if (lineItem.action === CRUD_OPERATION_TYPE.ADD) {
      updatedLineItem.action = CRUD_OPERATION_TYPE.ADD
      Object.assign(updatedLineItem, lineItem)
    } else if (lineItem.action === CRUD_OPERATION_TYPE.EDIT) {
      updatedLineItem.action = CRUD_OPERATION_TYPE.EDIT
      updatedLineItem.quantity = lineItem.quantity
      updatedLineItem.cleaningService = lineItem.cleaningService
      updatedLineItem.price = lineItem.price
      updatedLineItem.name = lineItem.name
      updatedLineItem.sublines = lineItem.sublines?.map(
        (subline): Partial<EditStateSubline> => {
          if (subline.action) {
            if (subline.action === CRUD_OPERATION_TYPE.DELETE) {
              return {
                action: subline.action,
                variantId:
                  subline?.type === SublineType.VARIANT
                    ? subline.variantId
                    : null,
              }
            } else {
              return {
                action: subline.action,
                type: subline.type,
                price: subline.price ?? 0,
                quantity: subline.quantity,
                comment: subline?.comment,
                addOnName: subline?.addOnName,
                variantId:
                  subline?.type === SublineType.VARIANT
                    ? subline.variantId
                    : null,
              }
            }
          } else {
            return {
              variantId:
                subline?.type === SublineType.VARIANT
                  ? subline.variantId
                  : null,
            }
          }
        }
      ) as Partial<EditStateSubline>[]
      updatedLineItem.subtotal = lineItem.subtotal
      updatedLineItem.tax = lineItem.tax
    }

    if (updatedLineItem.action) {
      updatedEditLineItemsData.push(updatedLineItem)
    } else {
      updatedEditLineItemsData.push({
        itemId: lineItem.itemId,
      })
    }
  })

  return updatedEditLineItemsData as Partial<EditStateLineItem>[]
}

export const transformCurrentTicketData = (
  currentTicketData: TicketData
): CurrentTicketData => {
  const lineItems = currentTicketData?.items
    ? currentTicketData?.items.map((item: Item, index: number) =>
        transformTicketItemData(item, index, currentTicketData.taxConfiguration)
      )
    : ([] as LineItem[])

  const memos = currentTicketData?.memos
    ? currentTicketData?.memos.map((item: Memo, index: number) =>
        transformTicketMemosData(item, index)
      )
    : ([] as Memo[])

  const priceAdjustments = currentTicketData?.priceAdjustments
    ? currentTicketData?.priceAdjustments.map(
        (item: PriceAdjustment, index: number) =>
          transformTicketPriceAdjustmentsData(item, index)
      )
    : ([] as PriceAdjustmentData[])

  return {
    storeCustomerId: currentTicketData?.storeCustomerId as number,
    lineItems,
    taxConfiguration: currentTicketData?.taxConfiguration || {},
    totalDiscount: currentTicketData?.totalDiscount as number,
    totalSurcharge: currentTicketData?.totalSurcharge as number,
    totalMemo: currentTicketData?.totalMemo as number,
    subtotal: currentTicketData?.subtotal as number,
    taxTotal: currentTicketData?.tax as number,
    total: currentTicketData?.total as number,
    notes: currentTicketData?.notes as string,
    memos,
    priceAdjustments: priceAdjustments as PriceAdjustmentData[],
    isEdit: true,
    currentTicketId: currentTicketData?.number,
    flagged: currentTicketData?.flagged,
    originalData: {
      flagged: currentTicketData?.flagged,
      notes: currentTicketData?.notes,
    },
    ...(currentTicketData.fulfillment
      ? {
          originalFulfillment: currentTicketData.fulfillment,
          fulfillment: currentTicketData.fulfillment,
        }
      : {}),
  }
}

export const transformTicketItemData = (
  currentTicketLineItemData: Item,
  itemIndex: number,
  taxConfiguration?: TaxConfiguration
): LineItem => {
  return {
    itemId: currentTicketLineItemData?.id,
    name: currentTicketLineItemData?.description,
    quantity: currentTicketLineItemData?.quantity,
    itemType: currentTicketLineItemData?.itemType?.toLowerCase() as string,
    price: currentTicketLineItemData?.unitPrice,
    tax: currentTicketLineItemData?.tax,
    subtotal: currentTicketLineItemData?.subtotal,
    sublines: currentTicketLineItemData?.sublines || [],
    taxRate: getTexRateValue(
      currentTicketLineItemData?.itemType?.toUpperCase() as string,
      taxConfiguration
    ),
    isQuantityModalOpen: false,
    uniqueId: uuidv4(),
    pricePerLb: currentTicketLineItemData?.pricePerLb,
    minLbs: currentTicketLineItemData?.minLbs,
    cleaningService: currentTicketLineItemData?.cleaningService,
    itemIndex,
    pieces: currentTicketLineItemData?.pieces,
  }
}

export const transformTicketMemosData = (
  currentTicketMemoData: Memo,
  memoIndex: number
): Memo => {
  return {
    type: currentTicketMemoData?.type,
    name: currentTicketMemoData?.name,
    subtotal: currentTicketMemoData?.subtotal,
    comment: currentTicketMemoData?.comment,
    memoIndex: memoIndex,
  }
}

export const transformTicketPriceAdjustmentsData = (
  currentTicketPriceAdjustmentData: PriceAdjustment,
  priceAdjustmentDataIndex: number
) => {
  return {
    priceAdjustmentId: currentTicketPriceAdjustmentData?.priceAdjustmentId,
    type: currentTicketPriceAdjustmentData?.type,
    name: currentTicketPriceAdjustmentData?.name,
    rate: currentTicketPriceAdjustmentData?.rate,
    subtotal: currentTicketPriceAdjustmentData?.subtotal,
    tax: currentTicketPriceAdjustmentData?.tax,
    comment: currentTicketPriceAdjustmentData?.comment,
    priceAdjustmentIndex: priceAdjustmentDataIndex,
    amount: currentTicketPriceAdjustmentData?.amount,
  }
}

export const transformPaymentReceiptData = (
  isPrintPaymentReceipt: boolean,
  ticketPrintData: TicketDataBE[],
  type: PrintOptions,
  store: Store,
  selectedTeamMember: TeamMember
): STMConfigType | null => {
  if (isPrintPaymentReceipt) {
    const billingEvents =
      ticketPrintData &&
      ticketPrintData?.flatMap((item) =>
        item.billingEvents
          ?.filter((event) => event.eventType === 'PAYMENT')
          .at(-1)
          ? [
              item.billingEvents
                .filter((event) => event.eventType === 'PAYMENT')
                .at(-1)!,
            ]
          : []
      )

    const latestEvent = billingEvents?.[0]
    const latestTimestamp =
      latestEvent &&
      convertToTimeZone(
        new Date(latestEvent.eventTimestamp),
        store.timeZone as string
      )

    const filteredEvents =
      latestTimestamp &&
      billingEvents &&
      billingEvents?.filter((event) => {
        const eventDate = convertToTimeZone(
          new Date(event.eventTimestamp),
          store.timeZone as string
        )
        return (
          isEqual(latestTimestamp, eventDate) ||
          isAfter(eventDate, latestTimestamp)
        )
      })

    const result = filteredEvents?.map((event) => {
      const details = { ...event?.details }
      return {
        paymentAmountTendered: details?.paymentAmountTendered || 0,
        cardAuthCode: details?.cardAuthCode,
        cardLastFour: details?.cardLastFour,
        cardEntryMode: details?.cardEntryMode,
        paymentAmountChange: details?.paymentAmountChange || 0,
        cardPaymentRetRef: details?.cardPaymentRetRef,
        cardEmvTagData: details?.cardEmvTagData,
        cardResponseText: details?.cardResponseText,
        paymentType: details?.paymentType,
        checkNumber: details?.checkNumber,
        totalAmountPaid: event?.amount,
        otherMethod: details?.otherMethod,
      }
    })

    const closeDate =
      ticketPrintData &&
      ticketPrintData?.[0]?.ticketStatus === TicketStatusForFilter.CLOSED
        ? ticketPrintData?.[0]?.closeTimestamp
        : ticketPrintData?.[0]?.openDate ||
          ticketPrintData?.[0]?.createTimestamp

    const adjustedDate = closeDate
      ? formatInTimeZone(
          closeDate,
          store?.timeZone || '',
          DATE_FORMAT_MM_DD_YY_HH_MM_A_WITHOUT_COMMA
        )
      : null

    return {
      store,
      option: type,
      storeData: {
        teamMemberName: ticketPrintData?.[0].createTeamMemberName as string,
        isPaymentReceipt: false,
      },
      paymentDetails: {
        ticketAndPaidAmount:
          ticketPrintData &&
          ticketPrintData?.map(({ ticketId, billingEvents }) => {
            const billingEventsFiltered = billingEvents
              ?.filter((event) => event.eventType === 'PAYMENT')
              .at(-1)
              ? [
                  billingEvents
                    .filter((event) => event.eventType === 'PAYMENT')
                    .at(-1)!,
                ]
              : []

            const latestTimestamp =
              latestEvent &&
              convertToTimeZone(
                new Date(latestEvent.eventTimestamp),
                store.timeZone as string
              )
            const filteredEvents =
              latestTimestamp &&
              billingEventsFiltered.length > 0 &&
              billingEventsFiltered?.filter((event) => {
                const eventDate = convertToTimeZone(
                  new Date(event.eventTimestamp),
                  store.timeZone as string
                )
                return (
                  isEqual(latestTimestamp, eventDate) ||
                  isAfter(eventDate, latestTimestamp)
                )
              })
            return {
              ticketId: formatTicketNumber(ticketId, false),
              totalAmountPaid:
                filteredEvents && filteredEvents?.length
                  ? filteredEvents.at(-1)!.amount
                  : 0,
            }
          }),
        totalAmountPaid: result?.reduce(
          (sum, { totalAmountPaid }) => sum + totalAmountPaid,
          0
        ),
        paymentAmountTendered: result?.reduce(
          (sum, { paymentAmountTendered }) => sum + paymentAmountTendered,
          0
        ),
        cardAuthCode: result?.[0]?.cardAuthCode || '',
        cardLastFour: result?.[0]?.cardLastFour || '',
        cardEntryMode: result?.[0]?.cardEntryMode || '',
        paymentAmountChange: result?.reduce(
          (sum, { paymentAmountChange }) => sum + paymentAmountChange,
          0
        ),
        cardPaymentRetRef: result?.[0]?.cardPaymentRetRef || '',
        cardEmvTagData: result?.[0]?.cardEmvTagData,
        cardResponseText: result?.[0]?.cardResponseText || false,
        paymentType: result?.[0]?.paymentType,
        eventType: billingEvents?.[0]?.eventType,
        checkNumber: result?.[0]?.checkNumber,
        otherMethod: result?.[0]?.otherMethod,
      },
      adjustedDate,
      isPrintPaymentReceipt,
      selectedTeamMember,
    } as STMConfigType
  }

  return null
}
