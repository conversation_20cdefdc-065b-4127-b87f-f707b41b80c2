// Define item type
type Item = {
  name: string
  icon: string
}

// Define category map
type ItemCategories =
  | 'TOP'
  | 'BOTTOM'
  | 'BUSINESS'
  | 'JACKETS'
  | 'ONE_PIECE'
  | 'LAUNDRY'
  | 'HOUSEHOLD'
  | 'ACCESSORIES'
  | 'FOOTWEAR'

type ItemsMap = {
  [key in ItemCategories]: Item[]
}

const items: ItemsMap = {
  TOP: [
    { name: 'SHIRT', icon: '/icon-library/top-shirt.svg' },
    { name: 'SWEATER', icon: '/icon-library/top-sweater.svg' },
    { name: '<PERSON><PERSON><PERSON>', icon: '/icon-library/top-polo.svg' },
    { name: 'BLOUS<PERSON>', icon: '/icon-library/top-blouse.svg' },
    { name: 'CARDIGAN', icon: '/icon-library/top-cardigan.svg' },
    { name: 'TURTLENECK', icon: '/icon-library/top-turtleneck.svg' },
    { name: 'T_SHIRT', icon: '/icon-library/top-t-shirt.svg' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: '/icon-library/top-hawaiian-shirt.svg' },
    { name: 'JERSEY', icon: '/icon-library/top-jersey.svg' },
    { name: 'CORSET', icon: '/icon-library/top-corset.svg' },
  ],
  BOTTOM: [
    { name: 'JEANS', icon: '/icon-library/bottom-jeans.svg' },
    { name: 'SKIRT', icon: '/icon-library/bottom-skirt.svg' },
    { name: 'SHORTS', icon: '/icon-library/bottom-shorts.svg' },
  ],
  BUSINESS: [
    {
      name: 'BUSINESS_DOUBLE_SUIT',
      icon: '/icon-library/business-double-suit.svg',
    },
    {
      name: 'BUSINESS_TRIPLE_SUIT',
      icon: '/icon-library/business-triple-suit.svg',
    },
    { name: 'BUSINESS_BLAZER', icon: '/icon-library/business-blazer.svg' },
    { name: 'BUSINESS_TROUSERS', icon: '/icon-library/business-trousers.svg' },
    {
      name: 'BUSINESS_WAISTCOAT',
      icon: '/icon-library/business-waistcoat.svg',
    },
  ],
  JACKETS: [
    { name: 'TRENCH_COAT', icon: '/icon-library/jacket-trench-coat.svg' },
    { name: 'PUFFER', icon: '/icon-library/jacket-puffer.svg' },
    { name: 'BLAZER', icon: '/icon-library/jacket-blazer.svg' },
    { name: 'VARSITY', icon: '/icon-library/jacket-varsity.svg' },
    { name: 'ZIP_UP', icon: '/icon-library/jacket-zip-up.svg' },
    { name: 'HOODIE', icon: '/icon-library/jacket-hoodie.svg' },
  ],
  ONE_PIECE: [
    { name: 'DRESS_PIECE', icon: '/icon-library/one-piece-dress.svg' },
    { name: 'JUMPSUIT_PIECE', icon: '/icon-library/one-piece-jumpsuit.svg' },
    { name: 'BODYSUIT_PIECE', icon: '/icon-library/one-piece-bodysuit.svg' },
    {
      name: 'BALLET_DRESS_PIECE',
      icon: '/icon-library/one-piece-ballet-dress.svg',
    },
    { name: 'BATHROBE_PIECE', icon: '/icon-library/one-piece-bathrobe.svg' },
    {
      name: 'CHEF_UNIFORM_PIECE',
      icon: '/icon-library/one-piece-chef-uniform.svg',
    },
  ],
  LAUNDRY: [
    { name: 'LAUNDRY_MACHINE', icon: '/icon-library/laundry-machine.svg' },
    {
      name: 'LAUNDRY_FOLDED',
      icon: '/icon-library/laundry-folded-clothes.svg',
    },
    { name: 'LAUNDRY_PAYMENT', icon: '/icon-library/laundry-payment.svg' },
    { name: 'LAUNDRY_BAG', icon: '/icon-library/laundry-bag.svg' },
    { name: 'DETERGENT_WHITE', icon: '/icon-library/detergent-white.svg' },
    { name: 'DETERGENT_YELLOW', icon: '/icon-library/detergent-yellow.svg' },
    { name: 'DETERGENT_GREEN', icon: '/icon-library/detergent-green.svg' },
    { name: 'DETERGENT_BLUE', icon: '/icon-library/detergent-blue.svg' },
    { name: 'DETERGENT_ORANGE', icon: '/icon-library/detergent-orange.svg' },
    { name: 'DETERGENT_RED', icon: '/icon-library/detergent-red.svg' },
  ],
  HOUSEHOLD: [
    { name: 'PILLOW', icon: '/icon-library/household-pillow.svg' },
    { name: 'CUSHION', icon: '/icon-library/household-cushion.svg' },
    { name: 'CURTAINS', icon: '/icon-library/household-curtains.svg' },
    { name: 'TOWELS', icon: '/icon-library/household-towels.svg' },
  ],
  ACCESSORIES: [
    { name: 'SCARF', icon: '/icon-library/accessory-scarf.svg' },
    { name: 'TIE', icon: '/icon-library/accessory-tie.svg' },
    { name: 'GLOVES', icon: '/icon-library/accessory-gloves.svg' },
    { name: 'HAT', icon: '/icon-library/accessory-hat.svg' },
    { name: 'HANDBAG_PINK', icon: '/icon-library/accessory-handbag-pink.svg' },
    {
      name: 'HANDBAG_WHITE',
      icon: '/icon-library/accessory-handbag-white.svg',
    },
    { name: 'NAPKIN', icon: '/icon-library/accessory-napkin.svg' },
    { name: 'BELT', icon: '/icon-library/accessory-belt.svg' },
    { name: 'SOCKS', icon: '/icon-library/accessory-socks.svg' },
  ],
  FOOTWEAR: [
    { name: 'BOOTS', icon: '/icon-library/footwear-boots.svg' },
    { name: 'HEELED_BOOTS', icon: '/icon-library/footwear-heeled-boots.svg' },
    { name: 'DRESS_SHOES', icon: '/icon-library/footwear-dress-shoes.svg' },
  ],
}

// Class definition
class ItemsIconLibrary {
  private items: ItemsMap
  private iconMap: Record<string, string>
  private nameToKeyMap: Record<string, string>

  constructor(items: ItemsMap) {
    this.items = items
    this.iconMap = this.buildIconMap()
    this.nameToKeyMap = this.buildNameToKeyMap()
  }

  // Generate key dynamically like "TOP_SHIRT"
  public generateKey(category: string, name: string): string {
    return `${category.toUpperCase()}_${name.toUpperCase().replace(/[-\s]/g, '')}`
  }

  // Internal: build a fast lookup map from items
  public buildIconMap(): Record<string, string> {
    const map: Record<string, string> = {}

    for (const category in this.items) {
      if (Object.prototype.hasOwnProperty.call(this.items, category)) {
        for (const item of this.items[category as ItemCategories]) {
          const key = this.generateKey(category, item.name)
          map[key] = item.icon
        }
      }
    }

    return map
  }

  // Map names to full icon keys for quick fallback lookup
  public buildNameToKeyMap(): Record<string, string> {
    const map: Record<string, string> = {}
    for (const category in this.items) {
      if (Object.prototype.hasOwnProperty.call(this.items, category)) {
        for (const item of this.items[category as ItemCategories]) {
          map[item.name.toUpperCase()] = this.generateKey(category, item.name)
        }
      }
    }
    return map
  }

  // Public: get icon by key
  public getIcon(keyOrName: string): string | undefined {
    // Try full key first (like JACKETS_ZIP_UP)
    if (this.iconMap[keyOrName]) {
      return this.iconMap[keyOrName]
    }

    // Try resolving from just name (like ZIP_UP)
    const keyFromName = this.nameToKeyMap[keyOrName.toUpperCase()]
    if (keyFromName && this.iconMap[keyFromName]) {
      return this.iconMap[keyFromName]
    }

    // Nothing found
    return undefined
  }

  // get item by key
  public getItem(key: string): { icon?: string; key: string } {
    return { icon: this.getIcon(key), key }
  }

  // get all items
  public getAllItems(): ItemsMap {
    return this.items
  }

  // get grouped items by category
  public getGroupedItems(): ItemsMap {
    return this.items
  }
}

// Export the instance of ItemsIconLibrary
export const iconLibrary = new ItemsIconLibrary(items)
