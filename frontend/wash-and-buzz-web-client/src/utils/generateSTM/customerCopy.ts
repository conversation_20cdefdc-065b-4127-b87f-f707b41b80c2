import { getLineItemPrice } from '@/components/core/Ticket/Calculations'
import { FulfillmentMethodLabel } from '@/components/Fulfillment/FulfillmentMethodButton'
import { getGroupedSublineData } from '@/serializer/tickets.serializers'
import { TextSale } from '@/types/module/commonModule'
import { CleaningServiceForBackendPayload } from '@/types/module/itemModule'
import { STMConfigType } from '@/types/module/ticketNdSalesModule'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import { getFormattedDueDate } from '@/utils/date'
import {
  convertToCurrency,
  getCleaningServiceLabel,
  isNonZeroAmount,
} from '@/utils/functions'
import { translation } from '@/utils/translation'

export const customerCopy = async (data: Partial<STMConfigType>) => {
  const {
    store,
    adjustedDate,
    ticketNumber,
    customer,
    fulfillmentDate,
    fulfillmentDetail,
    fulfillmentDay,
    numberOfPieces,
    isAddOnPricesEnabled,
    orderGroupedItemsData,
    notes,
    ticketSummary,
    ticketMessage,
    isMessageOnTicketEnabled,
    isStoreInfoEnabled,
    isTicketBarcodeEnabled,
    storeData,
  } = data

  const weighted = TextSale.WEIGHTED?.toString().toLocaleUpperCase()

  let stmContent = ``
  // Store Info based on setting
  if (isStoreInfoEnabled) {
    stmContent += `[plain]\
                  [align: center]\
                  [bold: on]\
                  [magnify: width 2; height 2]\
                  ${store?.name}
                  [plain]\
                  ${store?.address}
                  ${store?.city} ${store?.state} ${store?.zipCode}
                  ${store?.phoneNumber}
                  `
  }
  // Ticket Created Clerk/Time for Customer Copy & Store Copy
  stmContent += `[plain]\
                [align: center]\
                [magnify: width 1; height 1]
                Clerk: ${storeData?.teamMemberName}\
                [space: count 3]\
                Created: ${adjustedDate}\
                `
  // Line Separator
  stmContent += `[plain]\
                [align: center]\
                ········································`
  // Ticket #
  stmContent += `[plain]\
                [align: center]\
                [bold: on]\
                [magnify: width 5; height 4]\
                ${ticketNumber}
                `
  // Barcode based on settings
  if (isTicketBarcodeEnabled) {
    stmContent += `[barcode: type code39; data ${ticketNumber}; height 6mm;
              module 3]\
              `
  }

  // Fulfillment Method, Fulfillment Date, No.of Pieces, Pickup Day / Time
  if (
    fulfillmentDetail?.method ||
    fulfillmentDate ||
    fulfillmentDay ||
    numberOfPieces
  ) {
    stmContent += `[align: center] [plain] [bold: on] ${(fulfillmentDetail?.method && FulfillmentMethodLabel[fulfillmentDetail?.method as keyof typeof FulfillmentMethodLabel]) || ''} [plain]${
      (fulfillmentDetail &&
        fulfillmentDetail.date &&
        fulfillmentDetail.time &&
        getFormattedDueDate(fulfillmentDetail.date, fulfillmentDetail.time)) ||
      ''
    }
  [bold: on] [magnify: width 3; height 3] ${fulfillmentDay?.slice(0, 3) || ''} ${numberOfPieces && fulfillmentDay ? '-' : ''} ${numberOfPieces !== null && numberOfPieces ? numberOfPieces + ' pcs' : ''}
  `
  }

  // Customer Name,
  if (customer?.name) {
    stmContent += `[plain]\
                 [align: center]\
                 Customer:[magnify: width 2; height 1]\[bold: on]\ ${customer.name}
                 `
  }

  // Line Separator
  stmContent += `[plain]\
                [align: center]\
                ········································
                `

  // Consolidated Item Count , Line Item and Line Item Price , Subline and Subline Price and Alterations

  orderGroupedItemsData?.forEach(({ items, name, totalQuantity }) => {
    stmContent += `[plain]\
        [bold: on]\
        [align: left]\
        [magnify: width 2; height 1]\
        ${totalQuantity} ${name}
        `
    if (items && items?.length > 0) {
      items?.some((item) => {
        if (item.itemType === weighted) {
          stmContent += `[plain]\
            [space: count 4]\
            ${convertToCurrency(item?.price.toString())}(min ${item?.minLbs}lbs) + ${convertToCurrency(item?.pricePerLb?.toString() || '0')} per additional lb
            `
          return true
        }

        return false
      })

      items?.forEach((item) => {
        let groupedSublineData
        if (item?.sublines?.length > 0) {
          groupedSublineData = getGroupedSublineData(item?.sublines)
        }

        stmContent += `[magnify: width 2; height 1]\
          [bold: on]\
          [column: left ${item?.quantity} ${
            item.itemType === TextSale.CLEANING?.toString().toLocaleUpperCase()
              ? getCleaningServiceLabel(
                  item.cleaningService as CleaningServiceForBackendPayload
                )
              : item.itemType === weighted
                ? 'lbs'
                : item?.name
          }; right ${CURRENCY_FORMATTER.format(
            item.itemType === weighted
              ? getLineItemPrice(
                  item.price,
                  item.quantity,
                  item.itemType.toString().toLocaleLowerCase(),
                  item?.minLbs,
                  item?.pricePerLb
                )
              : item.price * Number(item.quantity)
          )}; indent 75]
          `

        //  Alteration
        groupedSublineData?.detailedSublines?.map((subline) => {
          stmContent += `[plain]\
          [magnify: width 2; height 1]\
          [column: left ${subline.addOnName} ${subline.comment ? ':' + subline.comment : ''}; right ${isAddOnPricesEnabled ? CURRENCY_FORMATTER.format(Number(subline?.price) * (item.quantity || 1)) : '.'}; indent 125]
          `
        })

        if (
          groupedSublineData !== undefined &&
          groupedSublineData?.groupedText !== undefined &&
          groupedSublineData?.groupedText?.length > 0
        ) {
          stmContent += `[plain]\
          [magnify: width 2; height 1]\
        [column: left ${groupedSublineData?.groupedText?.join(', ')}; right ${
          isAddOnPricesEnabled
            ? groupedSublineData?.groupedPrice
              ? CURRENCY_FORMATTER.format(
                  Number(groupedSublineData?.groupedPrice) *
                    (item?.itemType === weighted ? 1 : Number(item.quantity))
                )
              : CURRENCY_FORMATTER.format(0)
            : '.'
        }; indent 125]
        `
        }
      })
    }
  })

  // Credit card surcharge and Cash discount
  if (ticketSummary?.totalDiscount || ticketSummary?.totalSurcharge) {
    stmContent += `
            [plain]\
            [magnify: width 1; height 1]\
            [column: left ${ticketSummary?.totalDiscount ? translation.CASH_DISCOUNT : translation.CREDIT_CARD_SURCHARGE}; right ${ticketSummary?.totalDiscount || ticketSummary?.totalSurcharge}; indent 50]
            `
  }

  // Note (If no note exists, do not print this)
  if (notes) {
    stmContent += `[plain]\
                [align: left]
                Note: ${notes}
                `
  }

  // Ticket Summary
  stmContent += `[plain]\
                [align: center]\
                ______________________________________________
                [plain]\
                [column: left Subtotal; right ${ticketSummary?.subtotal}; indent 250]
                [column: left Tax; right ${ticketSummary?.tax}]
                [magnify: width 2; height 1]\
                [bold: on]\
                [column: left Total; right ${ticketSummary?.total}]
                `
  // Outstanding Balance
  if (ticketSummary?.OutstandingBalance !== null) {
    stmContent += `
                [plain]\
                [column: left Balance; right ${ticketSummary?.OutstandingBalance}; indent 250]
                `
    // Paid Status
    if (!isNonZeroAmount(ticketSummary?.OutstandingBalance || '')) {
      stmContent += `
                [plain]\
                [magnify: width 2; height 2]\
                [align: center]\
                [bold: on]\
                **PAID**
                `
    }
  }

  // Message (based on settings)
  if (isMessageOnTicketEnabled) {
    stmContent += `
                [plain]\
                [align: center]\
                ${ticketMessage}\
                `
  }
  // Ticket Type
  stmContent += `
                [plain]\
                [align: center]\
                [bold: on]\
                [magnify: width 1; height 1]
                Customer Copy
                `
  // Cut
  stmContent += `
  [cut: feed; partial]
  `

  return stmContent
}
