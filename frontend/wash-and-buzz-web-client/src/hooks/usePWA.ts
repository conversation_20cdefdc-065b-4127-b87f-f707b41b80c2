'use client'

import { useEffect, useState } from 'react'

interface PWAState {
  isOnline: boolean
  isInstalled: boolean
  canInstall: boolean
  isUpdateAvailable: boolean
  isStandalone: boolean
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export const usePWA = () => {
  const [state, setState] = useState<PWAState>({
    isOnline: true,
    isInstalled: false,
    canInstall: false,
    isUpdateAvailable: false,
    isStandalone: false,
  })

  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null)

  useEffect(() => {
    // Check if running in standalone mode (installed)
    const checkStandalone = () => {
      const isStandalone =
        window.matchMedia('(display-mode: standalone)').matches ||
        (window.navigator as any).standalone === true

      setState((prev) => ({ ...prev, isStandalone, isInstalled: isStandalone }))
    }

    // Check online status
    const updateOnlineStatus = () => {
      setState((prev) => ({ ...prev, isOnline: navigator.onLine }))
    }

    // Handle beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setState((prev) => ({ ...prev, canInstall: true }))
    }

    // Handle app installed event
    const handleAppInstalled = () => {
      setState((prev) => ({ ...prev, isInstalled: true, canInstall: false }))
      setDeferredPrompt(null)
    }

    // Initialize
    checkStandalone()
    updateOnlineStatus()

    // Add event listeners
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    window.addEventListener(
      'beforeinstallprompt',
      handleBeforeInstallPrompt as EventListener
    )
    window.addEventListener('appinstalled', handleAppInstalled)

    // Cleanup
    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt as EventListener
      )
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const installApp = async () => {
    if (!deferredPrompt) return false

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice

      if (outcome === 'accepted') {
        setState((prev) => ({ ...prev, canInstall: false }))
        setDeferredPrompt(null)
        return true
      }
      return false
    } catch (error) {
      console.error('Error installing app:', error)
      return false
    }
  }

  const shareApp = async (data?: ShareData) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'WashAndBuzz',
          text: 'A full-fledged solution for laundry owners',
          url: window.location.origin,
          ...data,
        })
        return true
      } catch (error) {
        console.error('Error sharing:', error)
        return false
      }
    }
    return false
  }

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  const showNotification = (title: string, options?: NotificationOptions) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        ...options,
      })
    }
    return null
  }

  return {
    ...state,
    installApp,
    shareApp,
    requestNotificationPermission,
    showNotification,
  }
}

export default usePWA
