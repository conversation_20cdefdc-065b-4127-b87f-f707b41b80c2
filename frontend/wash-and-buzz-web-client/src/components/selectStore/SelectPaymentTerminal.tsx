'use client'
import profileIcon from '@/../public/images/profile-icon.png'
import refreshIcon from '@/../public/images/refresh.svg'
import { ThemeButton } from '@/components/core/Button/Button'
import { CustomModalBtn } from '@/components/core/Button/CustomModalBtn'
import { PaymentTerminalSelection } from '@/components/selectStore/PaymentTerminalSelection'
import Spinner from '@/components/spinner/Spinner'
import { setIsIdeal } from '@/store/actions/common.action'
import { connectPaymentTerminalData } from '@/store/actions/paymentTerminal.action'
import { getStoreDataRequest } from '@/store/actions/store.actions'
import { statusPaymentTerminal } from '@/store/apis'
import { PaymentTerminalData } from '@/types/module/paymentTerminalModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import {
  FALSE,
  IS_PIN_MODAL_OPEN,
  IS_TEAM_MEMBER_SELECTED,
  TRUE,
} from '@/utils/constant'
import { appRoutes } from '@/utils/routes'
import { translation } from '@/utils/translation'
import Cookies from 'js-cookie'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Card, Text } from 'theme-ui'

export const SelectPaymentTerminal = () => {
  const paymentTerminalData = useSelector(
    (state: MainStoreType) => state.paymentTerminalData
  )
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const [refreshTerminalData, setRefreshTerminalData] = useState<boolean>(false)
  const [selectedPaymentTerminal, setSelectedPaymentTerminal] =
    useState<PaymentTerminalData | null>(null)
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [paymentTerminals, setPaymentTerminals] = useState<
    PaymentTerminalData[]
  >([])
  const fetchedTerminalsRef = useRef<Set<number>>(new Set())

  const hasFetched = useRef(false)

  const dispatch = useDispatch()

  const handleSelectedTerminal = (paymentTerminals: PaymentTerminalData[]) => {
    const userPreference = localStorage.getItem('userPreference')
    const savedPreference = userPreference ? JSON.parse(userPreference) : null
    if (savedPreference?.paymentTerminal) {
      const savedTerminal = paymentTerminals.find(
        (terminal) => terminal.hsn === savedPreference.paymentTerminal
      )
      if (savedTerminal) {
        setSelectedPaymentTerminal(savedTerminal)
      }
    }
  }

  // Function to fetch the payment terminal statuses from API
  const fetchPaymentTerminalStatuses = useCallback(async () => {
    if (paymentTerminalData.data && paymentTerminalData.data.length > 0) {
      setIsLoading(true)
      try {
        const updatedTerminals = await Promise.all(
          paymentTerminalData.data.map(async (terminal) => {
            if (!fetchedTerminalsRef.current.has(terminal.id as number)) {
              try {
                await statusPaymentTerminal(
                  storeValue.data?.id as number,
                  terminal.hsn
                )
                fetchedTerminalsRef.current.add(terminal.id as number)
                return terminal
              } catch (error) {
                console.error(
                  `Failed to fetch status for terminal: ${terminal.id}`
                )
                return null
              }
            }
            return terminal
          })
        )
        setPaymentTerminals(
          updatedTerminals.filter(Boolean) as PaymentTerminalData[]
        )
        handleSelectedTerminal(
          updatedTerminals.filter(Boolean) as PaymentTerminalData[]
        )
      } finally {
        setIsLoading(false)
      }
    }
  }, [paymentTerminalData.data, storeValue.data?.id, setIsLoading])

  useEffect(() => {
    if (storeValue.data === null) {
      dispatch(getStoreDataRequest())
    }
  }, [storeValue.data, dispatch])

  // Trigger fetching of payment terminals initially and on refresh
  useEffect(() => {
    if (
      !hasFetched.current &&
      paymentTerminalData.data?.length &&
      storeValue.data?.id
    ) {
      fetchPaymentTerminalStatuses()
      hasFetched.current = true
    }
  }, [
    fetchPaymentTerminalStatuses,
    paymentTerminalData.data,
    storeValue.data?.id,
  ])

  // Re-fetch terminal data when the refresh button is clicked
  useEffect(() => {
    if (refreshTerminalData) {
      fetchedTerminalsRef.current.clear()
      fetchPaymentTerminalStatuses()
      setRefreshTerminalData(false)
    }
  }, [refreshTerminalData, fetchPaymentTerminalStatuses])

  // Automatically select the connected terminal if available
  useEffect(() => {
    if (paymentTerminalData.data && paymentTerminalData.data.length > 0) {
      const connectedTerminal = paymentTerminalData.data.find(
        (terminal) => terminal.isConnected === 1
      )
      if (connectedTerminal) {
        setSelectedPaymentTerminal(connectedTerminal)
      }
    }
  }, [paymentTerminalData.data])

  const handlePaymentTerminalButtonClick = (
    paymentTerminal: PaymentTerminalData
  ) => {
    if (paymentTerminal.id !== selectedPaymentTerminal?.id) {
      setSelectedPaymentTerminal(paymentTerminal)
    } else {
      setSelectedPaymentTerminal(null)
    }
  }

  const handleUserModal = () => {
    const isTeamMemberSelected = Cookies.get(IS_TEAM_MEMBER_SELECTED)
    if (!isTeamMemberSelected || isTeamMemberSelected === FALSE) {
      localStorage.setItem(IS_PIN_MODAL_OPEN, TRUE)
      dispatch(setIsIdeal(true))
    } else {
      const appRoute =
        storeValue.data && storeValue.data?.posProductType > 1
          ? appRoutes.homePage
          : appRoutes.defaultState
      router.push(appRoute)
    }
  }

  const handleNextButtonClick = () => {
    const storedPreference = localStorage.getItem(translation.USER_PREFERENCE)
    const userPreference = storedPreference ? JSON.parse(storedPreference) : {}
    if (selectedPaymentTerminal) {
      userPreference.paymentTerminal = selectedPaymentTerminal.hsn
      localStorage.setItem(
        translation.USER_PREFERENCE,
        JSON.stringify(userPreference)
      )
      dispatch(
        connectPaymentTerminalData(
          { hsn: selectedPaymentTerminal?.hsn, isConnected: 1 },
          paymentTerminalData.data?.find((id) => id.isConnected === 1)?.hsn ||
            null,
          () => {
            handleUserModal()
          }
        )
      )
    } else {
      handleUserModal()
    }
  }

  return (
    <div className="row g-0 select-store-container">
      <div className="col-12">
        <div className="pl-30 pt-30">
          <Image
            height={100}
            width={100}
            src={profileIcon}
            alt={translation.ICON}
          />
        </div>
      </div>
      <div className="col-12">
        <div className="row g-0">
          <div className="col-6 mx-auto custom-scroll">
            <Card variant="selectStoreCard">
              <Text
                variant="Primary24Demi116"
                sx={{ color: 'primaryText', marginBottom: '30px' }}
                as={'p'}
              >
                {translation.SELECT_PAYMENT_TERMINAL}
              </Text>
              <div className="select-store-card-contain mb-30">
                <div style={{ position: 'relative' }}>
                  {isLoading && (
                    <div
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)', // Optional overlay effect
                        zIndex: 10,
                      }}
                    >
                      <Spinner visible={isLoading} />
                    </div>
                  )}
                </div>
                <div className="row g-0">
                  {!isLoading &&
                    (paymentTerminals.length > 0 ? (
                      paymentTerminals.map(
                        (value: PaymentTerminalData, index: number) => (
                          <PaymentTerminalSelection
                            key={index}
                            value={value}
                            index={index}
                            handlePaymentTerminalButtonClick={
                              handlePaymentTerminalButtonClick
                            }
                            isPaymentTerminalSelected={
                              value.id === selectedPaymentTerminal?.id
                            }
                          />
                        )
                      )
                    ) : (
                      <div className="col-12">
                        <div className="row no-terminal-text-height align-items-center text-center">
                          <div className="col-12">
                            <Text
                              variant="Secondary16Medium125"
                              sx={{ mb: '14px' }}
                            >
                              {translation.NO_AVAILABLE_TERMINAL}
                            </Text>
                            {paymentTerminalData.data &&
                              paymentTerminalData.data?.length > 0 && (
                                <ThemeButton
                                  sx={{ marginX: 'auto' }}
                                  className="cta-button"
                                  variant="textBtn"
                                  text={translation.REFRESH}
                                  icon={refreshIcon}
                                  onClick={() => {
                                    setRefreshTerminalData(true)
                                  }}
                                />
                              )}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div className="pr-30">
                <CustomModalBtn
                  cancelBtnTitle={translation.BACK}
                  submitBtnTitle={
                    selectedPaymentTerminal
                      ? translation.NEXT
                      : translation.SKIP
                  }
                  cancelBtnClick={() => {
                    router.push(appRoutes.selectPrinterData)
                  }}
                  submitBtnClick={() => {
                    handleNextButtonClick()
                  }}
                  submitBtnClassName="z-index-17 font-poppins"
                />
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
