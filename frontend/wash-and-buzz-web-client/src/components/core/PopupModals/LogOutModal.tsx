'use client'
import { ThemeButton } from '@/components/core/Button/Button'
import { PopupModalHeader } from '@/components/core/Text/Texts'
import Spinner from '@/components/spinner/Spinner'
import { useUserAccountActions } from '@/hooks/useUserAccountActions'
import { LogOutModalProps } from '@/types/module/logoutModule'
import { RequestedAction } from '@/types/module/ticketNdSalesModule'
import { LogoutState } from '@/types/store/reducers/logout.reducer'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { translation } from '@/utils/translation'
import React, { useEffect, useRef } from 'react'
import { useSelector } from 'react-redux'
import Modal from 'react-responsive-modal'
import { Box, Flex, Text } from 'theme-ui'
export const LogOutModal: React.FC<LogOutModalProps> = ({
  isOpen = false,
  onClose,
  closeIcon,
  title,
  showCloseIcon = false,
  modalContainer = '',
}) => {
  const { handleOptionClick } = useUserAccountActions()
  const logoutState: LogoutState = useSelector(
    (store: MainStoreType) => store.logoutUserData
  )

  const modalRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus()
    }
  }, [isOpen])

  return (
    <Modal
      open={isOpen}
      classNames={{
        modal: modalContainer,
      }}
      onClose={() => {
        if (!logoutState.loading) {
          onClose()
        }
      }}
      closeIcon={closeIcon}
      showCloseIcon={showCloseIcon}
      center
      focusTrapped={false}
    >
      <Box ref={modalRef} tabIndex={-1}>
        <PopupModalHeader title={title} />
        <Box className="container">
          <Text as="p" p={['30px 0']} variant="Secondary16Medium125">
            {translation?.LOGOUT_DESC}
          </Text>
          <Flex
            sx={{
              gap: '20px',
              justifyContent: 'space-evenly',
            }}
          >
            <ThemeButton
              autoFocus={false}
              className="cta-button"
              sx={{ width: '100%', p: '10.88px 30px' }}
              text={translation?.LOGOUT}
              variant="tertiary"
              onClick={() => {
                handleOptionClick(RequestedAction.LOGOUT)
              }}
            />
            <ThemeButton
              autoFocus={false}
              className="cta-button"
              sx={{ width: '100%', p: '10.88px 30px' }}
              text={translation?.CANCEL}
              onClick={onClose}
            />
          </Flex>
        </Box>
      </Box>
      <Spinner visible={logoutState.loading} />
    </Modal>
  )
}
