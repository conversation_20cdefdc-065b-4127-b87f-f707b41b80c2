import rushIcon from '@/../../public/images/rush-icon-dark.svg'
import { ThemeButton } from '@/components/core/Button/Button'
import { CustomModalBtn } from '@/components/core/Button/CustomModalBtn'
import { CustomDivider, PopupModalHeader } from '@/components/core/Text/Texts'
import DateSelector from '@/components/Fulfillment/DateSelector'
import {
  dailyWorkloadData,
  FulfillmentMethod,
  FulfillmentMethodButton,
  FulfillmentMethodLabel,
} from '@/components/Fulfillment/FulfillmentMethodButton'
import WorkLoadTracker from '@/components/Fulfillment/WorkLoadTracker'
import Spinner from '@/components/spinner/Spinner'
import { setOpenFulfillmentModalOpen } from '@/store/actions/currentTicketState.action'
import { addOrEditCurrentTicketData } from '@/store/actions/ticket.action'
import { getWorkLoadTrackerData } from '@/store/apis'
import {
  ColorCodingSettings,
  Fulfillment,
  FulfillmentModalProps,
} from '@/types/module/fulfillmentModule'
import { PaymentMethodType } from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import {
  DATE_FORMAT_MMMM_DD_YYYY,
  DATE_FORMAT_YYYY_MM_DD,
  deliveryTimeRangeSlots,
} from '@/utils/constant'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import {
  calculateFutureDate,
  convertTo12HourFormat,
  convertToTimeZone,
  formatDate,
} from '@/utils/date'
import { processWorkloadData } from '@/utils/functions'
import { translation } from '@/utils/translation'
import { isBefore, isSameDay, parseISO, startOfDay } from 'date-fns'
import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Modal from 'react-responsive-modal'
import { Box, Flex, Text } from 'theme-ui'

export const FulfillmentModal: React.FC<FulfillmentModalProps> = ({
  isOpen = false,
  onClose,
  closeIcon,
  title,
  showCloseIcon = false,
  isQuickDropoff = false,
  modalContainer = '',
  handlePrepay,
  loading,
  handleSave,
  actualTicketData,
}) => {
  const apiCallFlag = useRef(true)
  const dispatch = useDispatch()
  const { data } = useSelector((store: MainStoreType) => store.storeData)
  const currentTicketData = useSelector(
    (state: MainStoreType) => state.ticketsData
  )
  const now = convertToTimeZone(new Date(), data?.timeZone as string)
  const startOfToday = startOfDay(now)
  const [startDate, setStartDate] = useState(now)
  const [selectedMethod, setSelectedMethod] =
    useState<FulfillmentMethod | null>(FulfillmentMethod?.STORE_PICKUP)
  const [isRushSelected, setIsRushSelected] = useState<boolean>(false)
  const [workLoadData, setWorkLoadData] = useState<dailyWorkloadData[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const formatSelectedDate = formatDate(
    calculateFutureDate(
      now,
      data?.fulfillmentSettings?.storePickup.defaultDays || 1
    ),
    DATE_FORMAT_YYYY_MM_DD,
    data?.timeZone as string
  )
  const [selectedDate, setSelectedDate] = useState<string | null>(
    formatSelectedDate
  )

  const fulfillmentDataExist =
    (actualTicketData?.fulfillment &&
      Object.keys(actualTicketData?.fulfillment).length > 0) ||
    false

  const [selectedTime, setSelectedTime] = useState<number>()
  const [timeSlots, setTimeSlots] = useState<number[]>([])
  const [timeRangeSlots, setTimeRangeSlots] = useState<string[]>([])

  const generateWeek = (start: Date) => {
    return Array.from({ length: 7 }, (_, i) => {
      return calculateFutureDate(start, i)
    })
  }

  const weekDays = generateWeek(startDate)

  const handleNextWeek = () => {
    apiCallFlag.current = false
    setStartDate((prev) => calculateFutureDate(prev, 7))
  }

  const handlePreviousWeek = () => {
    if (!isSameDay(startDate, startOfToday)) {
      apiCallFlag.current = false
      setStartDate((prev) => calculateFutureDate(prev, -7))
    }
  }

  const handleDateClick = (date: Date) => {
    if (isRushSelected) {
      setIsRushSelected(isSameDay(startDate, date))
    }
    setSelectedDate(
      formatDate(date, DATE_FORMAT_YYYY_MM_DD, data?.timeZone as string)
    )
  }

  const handleMethodClick = (method: FulfillmentMethod) => {
    setSelectedMethod(method)
    updateTimeSlots(method, selectedDate)
  }

  const updateTimeSlots = useCallback(
    (method: FulfillmentMethod | null, date: string | null) => {
      if (!date) return
      if (method === FulfillmentMethod.STORE_PICKUP) {
        const timeData = data?.fulfillmentSettings?.storePickup?.times || []
        if (timeData && timeData.length > 0) {
          const timeDataWithHourFormat = timeData
            .map((value) => value)
            .sort((a, b) => a - b)
          if (!currentTicketData.currentTicket?.fulfillment) {
            const lastTimeSlot =
              timeDataWithHourFormat[timeDataWithHourFormat.length - 1]
            setSelectedTime(lastTimeSlot)
          }
          setTimeSlots(timeDataWithHourFormat)
        } else {
          setTimeSlots([])
        }
        setTimeRangeSlots([])
      } else if (method === FulfillmentMethod.DELIVERY) {
        setTimeRangeSlots(deliveryTimeRangeSlots)
        setTimeSlots([])
      }
    },
    // eslint-disable-next-line
    [data]
  )

  const handleRushClick = () => {
    setIsRushSelected(!isRushSelected)
    setSelectedDate(
      formatDate(now, DATE_FORMAT_YYYY_MM_DD, data?.timeZone as string)
    )
    if (!isRushSelected) {
      apiCallFlag.current = false
    }
    setStartDate(now)
  }

  const fetchWorkloadData = useCallback(async () => {
    if (!apiCallFlag.current && data) {
      try {
        setIsLoading(true)
        apiCallFlag.current = true
        const start = formatDate(
          startDate,
          DATE_FORMAT_YYYY_MM_DD,
          data?.timeZone as string
        )
        const end = formatDate(
          calculateFutureDate(startDate, 6),
          DATE_FORMAT_YYYY_MM_DD,
          data?.timeZone as string
        )
        const response = await getWorkLoadTrackerData(data.id, start, end)
        if (response) {
          const workLoadData = processWorkloadData(
            response.data?.data?.dailyWorkload,
            startDate,
            data?.timeZone as string
          )
          setWorkLoadData(workLoadData)
        }
        setIsLoading(false)
      } catch (error) {
        setIsLoading(false)
        console.error('Error fetching workload tracker data:', error)
      }
    }
  }, [data, startDate])

  useEffect(() => {
    if (isOpen) {
      fetchWorkloadData()
    }
  }, [isOpen, fetchWorkloadData])

  useEffect(() => {
    if (selectedDate && selectedMethod) {
      updateTimeSlots(selectedMethod, selectedDate)
    }
  }, [selectedDate, selectedMethod, updateTimeSlots])

  const handleButtonClick = (isSubmitClick: boolean) => {
    if (
      selectedTime !== null &&
      selectedTime !== undefined &&
      typeof selectedTime === 'number' &&
      selectedDate &&
      FulfillmentMethod
    ) {
      const fulfillment: Fulfillment = {
        method: selectedMethod as FulfillmentMethod,
        rush: isRushSelected,
        date: selectedDate as string,
        time: selectedTime as number,
      }

      const originalFulfillment = currentTicketData?.currentTicket
        ?.originalFulfillment as Fulfillment | undefined

      const keysToCompare: (keyof Fulfillment)[] = [
        'method',
        'rush',
        'date',
        'time',
      ]

      const isEdit = originalFulfillment
        ? keysToCompare.some(
            (key) => fulfillment[key] !== originalFulfillment[key]
          )
        : false

      fulfillment.isEdit = isEdit

      if (isSubmitClick) {
        dispatch(
          addOrEditCurrentTicketData(
            {
              ...currentTicketData?.currentTicket,
              fulfillment: fulfillment,
            },
            null, // TODO: Once we have a edit flow for fulfillment detail we have to send the respective action from here.
            null
          )
        )

        if (currentTicketData?.currentTicket?.isEdit) {
          dispatch(setOpenFulfillmentModalOpen(false))
          return
        }

        handleSave(
          {
            amount: 0,
            type: PaymentMethodType.PAY_LATER,
          },
          fulfillment
        )
      } else {
        handlePrepay(fulfillment)
      }
    } else {
      console.error(
        'Invalid submission: Check selectedTime, selectedDate, and selectedMethod.'
      )
    }
  }

  const handlePrepayClick = () => {
    currentTicketData.currentTicket?.total !== 0 && handleButtonClick(false)
  }

  const getMethodLabel = (method: FulfillmentMethod) => {
    return method === FulfillmentMethod.STORE_PICKUP
      ? FulfillmentMethodLabel.STORE_PICKUP
      : FulfillmentMethodLabel.DELIVERY
  }

  useEffect(() => {
    if (currentTicketData.currentTicket?.fulfillment) {
      let { date, method, rush, time } =
        currentTicketData.currentTicket?.fulfillment

      const today = startOfDay(
        convertToTimeZone(new Date(), data?.timeZone as string)
      )
      const selectedDateObj = parseISO(date)

      if (isBefore(selectedDateObj, today)) {
        date = formatDate(
          today,
          DATE_FORMAT_YYYY_MM_DD,
          data?.timeZone as string
        )
        const timeData = data?.fulfillmentSettings?.storePickup?.times || []
        const timeDataWithHourFormat = timeData
          .map((value) => value)
          .sort((a, b) => a - b)
        time = timeDataWithHourFormat[timeDataWithHourFormat.length - 1]
      }
      setIsRushSelected(rush)
      setSelectedDate(date)
      setSelectedTime(time)
      setSelectedMethod(method)
    }
  }, [currentTicketData, data])

  const handleClickPayLater = () => {
    handleButtonClick(true)
  }

  useEffect(() => {
    if (!data?.fulfillmentSettings?.storePickup?.defaultDays) {
      apiCallFlag.current = false
      return
    }
    let today = convertToTimeZone(new Date(), data?.timeZone as string)
    const defaultDays = data.fulfillmentSettings.storePickup.defaultDays
    const selectedDate = calculateFutureDate(today, defaultDays)
    while (true) {
      const currentWeek = generateWeek(today)
      if (
        currentWeek.findIndex((date) => isSameDay(date, selectedDate)) !== -1
      ) {
        setStartDate(currentWeek[0])
        apiCallFlag.current = false
        break
      }
      today = calculateFutureDate(today, 7)
    }
  }, [data])

  return (
    <Modal
      open={isOpen}
      classNames={{
        modal: modalContainer,
      }}
      onClose={onClose}
      closeIcon={closeIcon}
      showCloseIcon={showCloseIcon}
      center
      focusTrapped={false}
    >
      <Box tabIndex={-1} className="custom-scroll">
        <PopupModalHeader title={title} />
        <Box
          px="20px"
          className="my-10 pt-10"
          sx={{ maxHeight: 'calc(581px - 70px)', overflowY: 'auto' }}
        >
          {data?.fulfillmentSettings?.pickupDelivery?.enabled && (
            <>
              <Text as="p" variant="Poppins16Medium20">
                {translation.FULFILLMENT_OPTION}
              </Text>
              <Flex
                px="0"
                className="gap-8 pt-10 pb-20 justify-content-between"
              >
                {Object.values(FulfillmentMethod)
                  .filter((method) => method !== FulfillmentMethod.NONE)
                  .map((method) => (
                    <FulfillmentMethodButton
                      key={method}
                      method={method}
                      text={getMethodLabel(method)}
                      disabled={method === FulfillmentMethod.DELIVERY}
                      selectedMethod={selectedMethod}
                      onClick={handleMethodClick}
                    />
                  ))}
              </Flex>
            </>
          )}
          {selectedMethod === FulfillmentMethod.STORE_PICKUP && (
            <Fragment>
              <Text py="10px" as="p" variant="Poppins16Medium20">
                {translation.READY_BY}
                <Text
                  className={`${!selectedDate ? 'text-secondary opacity-50' : ''}`}
                >
                  {selectedDate &&
                    formatDate(
                      parseISO(selectedDate),
                      DATE_FORMAT_MMMM_DD_YYYY,
                      data?.timeZone as string
                    )}
                </Text>
              </Text>

              <div className="row g-0 justify-content-between custom-scroll position-relative">
                <div className="calendar-wrapper p-20 mr-10">
                  {data && (
                    <div className="row g-0 justify-content-between align-items-center">
                      <div className="rush-wrapper">
                        <ThemeButton
                          className={`font-poppins cursor-pointer ${
                            isRushSelected ? 'rush-selected' : 'rush-default'
                          }`}
                          sx={{ minWidth: '152px', height: '64px' }}
                          variant="rushButton"
                          text={translation.RUSH_TODAY}
                          onClick={handleRushClick}
                          icon={rushIcon}
                          iconClassName={
                            isRushSelected
                              ? 'rush-icon-selected'
                              : 'rush-icon-default'
                          }
                        />
                      </div>
                      <DateSelector
                        timezone={data?.timeZone}
                        initialDate={new Date()}
                        weekDays={weekDays}
                        handleNextWeek={handleNextWeek}
                        handlePreviousWeek={handlePreviousWeek}
                        handleDateClick={handleDateClick}
                        startDate={startDate}
                        startOfToday={startOfToday}
                        selectedDate={parseISO(selectedDate as string)}
                      />
                    </div>
                  )}
                  {data && workLoadData.length > 0 && (
                    <WorkLoadTracker
                      workLoadData={workLoadData}
                      colorCodingSettings={
                        data?.fulfillmentSettings?.loadManagement
                          ?.colorCoding as ColorCodingSettings
                      }
                    />
                  )}
                </div>

                <div
                  className={`${data?.posProductType === 2 ? 'time-range-wrapper-cleaning-weight' : 'time-range-wrapper'}`}
                >
                  {(selectedMethod === FulfillmentMethod.STORE_PICKUP
                    ? timeSlots
                    : timeRangeSlots
                  ).map((slot, index) => (
                    <ThemeButton
                      key={index}
                      className={`mb-8 font-poppins cursor-pointer w-100 ${selectedTime === slot ? 'selected-time' : ''}`}
                      variant="timeRangeButton"
                      text={convertTo12HourFormat(slot)}
                      onClick={() => {
                        if (selectedMethod === FulfillmentMethod.STORE_PICKUP) {
                          setSelectedTime(slot as number)
                        }
                      }}
                    />
                  ))}
                </div>
                {(isLoading || loading) && <Spinner visible />}
              </div>
            </Fragment>
          )}
        </Box>
        <CustomDivider sx={{ p: 0, m: 0 }} />
        <CustomModalBtn
          wrapperSx={{
            px: '20px',
            pt: '20px',
            justifyContent: 'end',
          }}
          cancelBtnSx={{
            mr: '16px',
          }}
          submitBtnClassName="cta-button"
          cancelBtnClassName={`cta-button ${currentTicketData?.currentTicket?.total === 0 ? 'd-none' : ''} `}
          cancelBtnVariant="secondary"
          submitBtnTitle={
            currentTicketData?.currentTicket?.isEdit
              ? translation.APPLY
              : translation.CREATE_TICKET
          }
          cancelBtnTitle={
            fulfillmentDataExist || isQuickDropoff
              ? ''
              : `${translation.PREPAY} ${CURRENCY_FORMATTER.format(
                  currentTicketData?.currentTicket?.total || 0
                )}`
          }
          submitBtnDisabled={
            typeof selectedTime === 'number' && selectedDate && isQuickDropoff
              ? false
              : currentTicketData.currentTicket &&
                  currentTicketData.currentTicket?.total >= 0
                ? false
                : !fulfillmentDataExist
          }
          submitBtnClick={handleClickPayLater}
          cancelBtnClick={handlePrepayClick}
          cancelBtnDisabled={
            !(
              typeof selectedTime === 'number' &&
              selectedDate &&
              FulfillmentMethod &&
              currentTicketData.currentTicket?.total !== 0
            )
          }
        />
      </Box>
      <Spinner visible={isLoading} />
    </Modal>
  )
}
