'use client'
import calendarEditIcon from '@/../../public/images/calendar-edit.svg'
import moreButton from '@/../../public/images/horizontal-menu.svg'
import saveTicketIcon from '@/../../public/images/save-white-icon.svg'
import chargeCheckoutDisabled from '@/../public/images/charge-checkout-disabled.svg'
import chargeCheckout from '@/../public/images/charge-checkout-enabled.svg'
import disableHand from '@/../public/images/disable-hand.svg'
import disableFlag from '@/../public/images/flag-disable.svg'
import selectedFlag from '@/../public/images/selected-flag.svg'
import { ThemeButton } from '@/components/core/Button/Button'
import { CommonConfirmationModal } from '@/components/core/PopupModals/CommonConfirmationModal'
import ConfirmLeaveModal from '@/components/core/PopupModals/ConfirmLeaveModal'
import { FulfillmentModal } from '@/components/core/PopupModals/FulfillmentModal'
import { ItemWeightModal } from '@/components/core/PopupModals/ItemWeightModal'
import { MoreOptionsModal } from '@/components/core/PopupModals/MoreOptionsModal'
import { RefundTicketModal } from '@/components/core/PopupModals/RefundModal'
import { VoidTicketModal } from '@/components/core/PopupModals/VoidTicketModal'
import {
  getPaymentData,
  updateAmounts,
  updatePayloadData,
  updatePayloadDataForVoid,
} from '@/components/core/Ticket/Calculations'
import { CurrentTicketItemLine } from '@/components/core/Ticket/CurrentTicketItemLine'
import { CurrentTicketMemo } from '@/components/core/Ticket/CurrentTicketMemo'
import { CurrentTicketPriceAdjustment } from '@/components/core/Ticket/CurrentTicketPriceAdjustment'
import PaymentModalWrapper from '@/components/core/Ticket/PaymentModalWrapper'
import { ServiceModal } from '@/components/core/Ticket/ServiceModal'
import { showErrorToast, showToast } from '@/components/core/Toast/CustomToast'
import Spinner from '@/components/spinner/Spinner'
import useBlockNavigation from '@/hooks/useBlockNavigation'
import useIsVariantRequirementFulfilled from '@/hooks/useRequiredVariantsCheck'
import useTicketContainsOnlySalesOrOtherItems from '@/hooks/useTicketContainsOnlySalesOrOtherItems'
import { useTicketMoreButtonState } from '@/hooks/useTicketMoreButtonState'
import { useTicketPrint } from '@/hooks/useTicketPrint'
import { useUpdateCustomerTickets } from '@/hooks/useUpdateCustomerTickets'
import { useUserAccountActions } from '@/hooks/useUserAccountActions'
import { modifyTaxValue } from '@/serializer/store.serializer'
import {
  updatedTicketData,
  updatedTicketPayloadData,
} from '@/serializer/tickets.serializers'
import {
  setCashDrawerPermission,
  setIsEditTicket,
  setIsPrintButtonExist,
  setOnPaymentPrintReceipt,
  setTicketTotalAmount,
} from '@/store/actions/common.action'
import {
  setAssignedVariantData,
  setInitialRender,
  setIsButtonDisabled,
  setIsItemSelected,
  setOpenCardManualPaymentModal,
  setOpenCardPaymentModal,
  setOpenCashPaymentModal,
  setOpenCheckPaymentOptionModal,
  setOpenFulfillmentModalOpen,
  setOpenLeaveConfirmationModal,
  setOpenMoreOptionsModalOpen,
  setOpenOtherPaymentOptionModal,
  setOpenPayLaterModal,
  setOpenPaymentCustomAmountModal,
  setOpenPaymentOptionModal,
  setOpenPaymentPrintModal,
  setOpenReopenTicketModal,
  setOpenTicketCreatedModal,
  setOpenVoidConfirmationModal,
  setOpenVoidTicketModal,
  setQuantityForItem,
  setQuantityModalOpen,
  setRequestedAction,
  setRequestedRoute,
  setRequiredVariants,
  setSelectedItem,
  setSelectedItemIndex,
  setSelectedMemoIndex,
  setSelectedPriceAdjustment,
  setSelectedPriceAdjustmentIndex,
  setTotalLineItem,
  setVariantModalOpen,
  setWeightModalOpen,
} from '@/store/actions/currentTicketState.action'
import {
  resetSelectedCustomer,
  setSelectedCustomerTicketData,
} from '@/store/actions/customer.action'
import {
  addOrEditCurrentTicketData,
  addTicketsData,
  clearCurrentTicket,
  deleteCurrentTicket,
  setFlagButtonState,
  setLinItemState,
  setNextButtonState,
  setNoteState,
  ticketPrintSucceededAction,
  updateCurrentTicketData,
  updateTicketPaymentData,
} from '@/store/actions/ticket.action'
import { TextSale, ToastType } from '@/types/module/commonModule'
import { Fulfillment } from '@/types/module/fulfillmentModule'
import {
  ItemSelection,
  selectedItemData,
  SelectedPriceAdjustmentData,
} from '@/types/module/itemModule'
import { PaymentIncentivesType } from '@/types/module/priceIncentiveModule'
import { PosLayout } from '@/types/module/storeModule'
import { TaxRates, TaxSale } from '@/types/module/taxSaleModule'
import {
  ButtonState,
  CurrentTicketProps,
  EDIT_TICKET_ACTION_TYPE,
  LineItem,
  LineItemState,
  Memo,
  NoteState,
  PaidAmountData,
  PaymentMethodType,
  PaymentTypes,
  PosLayoutMode,
  PriceAdjustmentData,
  RequestedAction,
  TicketDataBE,
} from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { FLAG, NEXT_BUTTON_ID, SAVE_BUTTON_ID } from '@/utils/constant'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import { formatTimeLabel } from '@/utils/date'
import {
  calculatePayableAmount,
  findItemIndexBySelectedIndex,
  getPaymentIncentiveData,
  isP2AndP3ItemTypesExist,
  roundAmountByTwoDigits,
} from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import { formatTicketNumber } from '@/utils/ticket'
import { translation } from '@/utils/translation'
import { AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box, Card, Divider, Flex, Text } from 'theme-ui'

interface GroupedItems {
  name: string
  items: LineItem[]
  totalQuantity: number
}
interface PriceAdjustmentGroupedItems {
  name: string
  items: PriceAdjustmentData[]
  totalCount: number
}

interface SelectedItemData {
  itemIndex: number
  itemId: string
  name: string
  quantity: number
}

interface MemoGroupedItems {
  name: string
  items: Memo[]
  totalMemoCount: number
}
const CurrentTicket: FC<CurrentTicketProps> = ({
  clearEmptySublines,
  onClickDelete,
  variantRef,
  handleClickSaveEdit,
  onScrollToRequired,
  scrollToBottom,
  executeIfEditable,
  actualTicketData,
}) => {
  const { handleOptionClick } = useUserAccountActions()
  const updateCustomerTickets = useUpdateCustomerTickets()
  const dispatch = useDispatch()
  const itemLineContainerRef = useRef<HTMLDivElement>(null)
  const itemWeightModalRef = useRef<HTMLDivElement>(null)
  const serviceModalRef = useRef<HTMLDivElement>(null)
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const currentTicketData = useSelector(
    (state: MainStoreType) => state.ticketsData
  )
  const currentPaymentIncentive = useSelector((state: MainStoreType) => {
    const paymentIncentives = Array.isArray(state.priceIncentiveData.data)
      ? state.priceIncentiveData.data
      : []
    return paymentIncentives.find(
      (paymentIncentive) => paymentIncentive.isSelected
    )
  })
  const currentTicketState = useSelector(
    (state: MainStoreType) => state.currentTicketState
  )

  const teamMemberData = useSelector(
    (state: MainStoreType) => state.teamMemberData
  )

  const {
    isTicketClosedAndUnpaid,
    shouldShowMoreText,
    isTicketOpen,
    isTicketClosed,
  } = useTicketMoreButtonState(actualTicketData)

  const { handlePrintReceipt } = useTicketPrint()
  const [shouldBlock, setShouldBlock] = useState(true)
  const [selectedItemData, setSelectedItemData] = useState<SelectedItemData>()
  const isVariantRequirementFulfilled = useIsVariantRequirementFulfilled()
  const [payableAmount, setPayableAmount] = useState<number>()
  const [paymentType, setPaymentType] = useState<string>()
  const [reason, setReason] = useState<string>('')
  const [removeTicket, setRemoveTicket] = useState<boolean>(false)
  const [refundTicket, setRefundTicket] = useState<boolean>(false)
  const [paidValue, setPaidValue] = useState<number>()
  const [groupItemByName, setGroupItemByName] = useState<GroupedItems[]>()
  const [groupMemosByName, setGroupMemosByName] = useState<MemoGroupedItems[]>()
  const [groupPriceAdjustmentByName, setGroupPriceAdjustmentByName] =
    useState<PriceAdjustmentGroupedItems[]>()
  const [loading, setIsLoading] = useState<boolean>(false)
  const discountType = useSelector((state: MainStoreType) => {
    if (Array.isArray(state.taxSaleData.data)) {
      const taxSaleData = state.taxSaleData.data
      return taxSaleData.find((taxSale: TaxSale) => taxSale.isSelected)?.name ==
        'NET_SALES'
        ? 'PRE_TAX'
        : 'POST_TAX'
    }
    return 'PRE_TAX'
  })

  const totalAmountNeedsToPay = useMemo(() => {
    return updateAmounts(
      {
        ...structuredClone(currentTicketData?.currentTicket),
      },
      discountType
    ).total
  }, [currentTicketData?.currentTicket, discountType])

  const hasUnresolvedVariants = useMemo(() => {
    return (
      currentTicketState.requiredVariants !== null &&
      currentTicketState.requiredVariants > 0
    )
  }, [currentTicketState.requiredVariants])

  const checkIsSaveButtonEnabled: boolean = useMemo(() => {
    return (
      hasUnresolvedVariants ||
      currentTicketData.currentTicket?.lineItems?.length === 0
    )
  }, [hasUnresolvedVariants, currentTicketData?.currentTicket])

  const { originalPush, cancelNavigation } = useBlockNavigation({
    shouldBlock: currentTicketData.currentTicket?.isEdit ? shouldBlock : false,
    onBlock: (triggeredByBack) => {
      if (triggeredByBack) {
        dispatch(setRequestedRoute(appRoutes.tickets))
        dispatch(setOpenLeaveConfirmationModal(true))
      }
    },
  })

  const updateSelectedLineItem = useCallback(
    (index?: number) => {
      if (
        currentTicketData.currentTicket?.lineItems &&
        currentTicketData.currentTicket?.lineItems?.length > 0 &&
        index !== undefined
      ) {
        const lineItems = structuredClone(
          currentTicketData.currentTicket?.lineItems
        )
        if (index !== undefined) {
          lineItems[index].isSelected = true
        }
        dispatch(
          addOrEditCurrentTicketData(
            updateAmounts(
              {
                ...currentTicketData?.currentTicket,
                lineItems,
              },
              discountType
            ),
            null,
            null
          )
        )
      }
      if (
        currentTicketData.currentTicket?.lineItems &&
        currentTicketData.currentTicket?.lineItems?.length > 0 &&
        currentTicketState.selectedItemIndex === null &&
        index !== undefined
      ) {
        const lineItems = structuredClone(
          currentTicketData.currentTicket?.lineItems
        )
        if (index !== undefined) {
          lineItems[index].isSelected = true
        }
        dispatch(
          addOrEditCurrentTicketData(
            updateAmounts(
              {
                ...currentTicketData?.currentTicket,
                lineItems,
              },
              discountType
            ),
            null,
            null
          )
        )
      }
      clearEmptySublines()
      if (currentTicketState?.isItemSelected) {
        dispatch(setRequiredVariants(null))
        dispatch(setIsItemSelected(false))
        dispatch(setIsButtonDisabled(false))
      }
      if (currentTicketState?.variantModalOpen) {
        dispatch(setVariantModalOpen(false))
      }
    },
    [
      clearEmptySublines,
      currentTicketData.currentTicket,
      currentTicketState?.isItemSelected,
      currentTicketState.selectedItemIndex,
      currentTicketState?.variantModalOpen,
      discountType,
      dispatch,
    ]
  )

  const ticketContainsOnlySalesAndOtherItems =
    useTicketContainsOnlySalesOrOtherItems(actualTicketData)

  const handleItemClick = useCallback(
    (itemIndex: number, itemId: string, name: string, quantity: number) => {
      if (
        !currentTicketState.requiredVariants &&
        currentTicketState.selectedItemIndex !== itemIndex
      ) {
        dispatch(setInitialRender(true))
        updateSelectedLineItem(itemIndex)
        dispatch(setSelectedItemIndex(itemIndex))
        dispatch(setQuantityForItem(quantity))
        dispatch(
          setSelectedItem({
            itemId,
            name,
            selectFrom: ItemSelection.TICKET,
            index: itemIndex,
          })
        )
        dispatch(setNoteState(null))
        dispatch(setLinItemState(LineItemState.Focused))
      }
    },
    [
      currentTicketState.requiredVariants,
      currentTicketState.selectedItemIndex,
      dispatch,
      updateSelectedLineItem,
    ]
  )

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.lineItemState !== LineItemState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused) {
      handleItemClick(
        selectedItemData?.itemIndex as number,
        selectedItemData?.itemId as string,
        selectedItemData?.name as string,
        selectedItemData?.quantity as number
      )
    }
  }, [handleItemClick, selectedItemData, currentTicketData])

  const handlePriceAdjustmentClick = (
    priceAdjustmentIndex: number,
    priceAdjustmentId: number,
    name: string
  ) => {
    dispatch(setSelectedPriceAdjustmentIndex(priceAdjustmentIndex))
    dispatch(
      setSelectedPriceAdjustment({
        priceAdjustmentId,
        name,
        selectFrom: ItemSelection.TICKET,
      })
    )
  }

  const checkIsButtonDisabled = (): boolean => {
    if (
      (currentTicketData.currentTicket?.lineItems &&
        currentTicketData.currentTicket.lineItems?.length > 0) ||
      (currentTicketData.currentTicket?.memos &&
        currentTicketData.currentTicket.memos?.length > 0)
    ) {
      return currentTicketState.requiredVariants !== null
        ? false
        : currentTicketState?.isButtonDisabled
    } else {
      return currentTicketState.requiredVariants ? false : true
    }
  }

  const getButtonText = (): string => {
    return currentTicketState.selectedItemIndex !== null
      ? currentTicketState.requiredVariants !== null &&
        currentTicketState.requiredVariants > 0
        ? `${translation.MAKE_REQUIRED_CHOICES} (${currentTicketState?.requiredVariants})`
        : `${translation.NEXT} ${CURRENCY_FORMATTER.format(
            currentTicketData?.currentTicket?.total || 0
          )}`
      : `${translation.NEXT} ${CURRENCY_FORMATTER.format(
          currentTicketData?.currentTicket?.total || 0
        )}`
  }
  const [isCheck, setIsCheck] = useState<boolean>(false)

  const handlePaidAmount = useCallback(
    (paidAmount: PaidAmountData | null, fulfillment?: Fulfillment) => {
      setPaymentType(paidAmount?.type)
      if (currentTicketData?.currentTicket?.total !== undefined) {
        setIsLoading(true)
        const currentTicket = updateAmounts(
          {
            ...structuredClone(currentTicketData?.currentTicket),
            notes: currentTicketData?.currentTicket?.notes,
          },
          discountType
        )
        const totalAmountsNeedsToPay =
          (currentPaymentIncentive?.name ===
            PaymentIncentivesType.CASH_DISCOUNT &&
            paidAmount?.type === PaymentMethodType.CASH) ||
          (currentPaymentIncentive?.name ===
            PaymentIncentivesType.CREDIT_CARD_SURCHARGE &&
            paidAmount?.type === PaymentMethodType.CREDIT_CARD)
            ? (payableAmount as number)
            : currentTicket.total
        let paymentData
        if (
          paidAmount?.type &&
          paidAmount?.type !== PaymentMethodType.PAY_LATER
        ) {
          let paymentIncentive
          if (totalAmountsNeedsToPay > 0) {
            paymentIncentive = getPaymentIncentiveData(
              paidAmount?.type as PaymentMethodType,
              Math.min(totalAmountsNeedsToPay, paidAmount?.amount),
              currentPaymentIncentive,
              paidAmount?.type == PaymentMethodType.CASH &&
                paidAmount?.amount >= totalAmountsNeedsToPay
                ? currentTicket.total
                : undefined
            )
          }
          const changeAmount = roundAmountByTwoDigits(
            paidAmount &&
              paidAmount.type === PaymentMethodType.CASH &&
              paidAmount.amount > totalAmountsNeedsToPay
              ? paidAmount.amount -
                  currentTicket.total -
                  (paymentIncentive?.amount || 0)
              : 0
          )
          paymentData = getPaymentData(
            paidAmount?.type,
            changeAmount,
            0,
            paidAmount as PaidAmountData,
            paymentIncentive
          )
        }
        dispatch(
          addTicketsData(
            {
              ...currentTicket,
              notes: currentTicketData?.currentTicket?.notes,
              fulfillment:
                fulfillment || currentTicketData.currentTicket.fulfillment,
              flagged: currentTicketData.currentTicket.flagged,
              createTeamMemberId: teamMemberData?.selectedTeamMember
                ?.teamMemberId as string,
              total: parseFloat(currentTicket.total?.toFixed(2) || '0'),
              taxConfiguration: {
                rates: modifyTaxValue(storeValue.data?.taxRates as TaxRates),
                calculationBase: discountType,
              },
            },
            paymentData,
            (res, ticketPrintData, errorMessage) => {
              if (res) {
                const isPayLater =
                  paidAmount && paidAmount.type === PaymentMethodType.PAY_LATER
                setPaidValue(isPayLater ? 0 : paidAmount?.amount)
                if (isPayLater && ticketPrintData) {
                  handlePrintReceipt(ticketPrintData)
                }
                dispatch(deleteCurrentTicket())
                setIsLoading(false)
                if (paidAmount && paidAmount.type === PaymentMethodType.CHECK) {
                  dispatch(setOpenCheckPaymentOptionModal(false))
                  setIsCheck(true)
                  dispatch(setCashDrawerPermission(true))
                }
                if (
                  paidAmount &&
                  paidAmount.type === PaymentMethodType.CREDIT_CARD
                ) {
                  dispatch(setOpenCardPaymentModal(false))
                  dispatch(setOpenCardManualPaymentModal(false))
                }
                if (paidAmount && paidAmount.type === PaymentMethodType.OTHER) {
                  dispatch(setOpenOtherPaymentOptionModal(false))
                  setIsCheck(true)
                }
                if (isPayLater) {
                  dispatch(setOpenPayLaterModal(false))
                  dispatch(setOpenTicketCreatedModal(true))
                }
                if (paidAmount && paidAmount.type === PaymentMethodType.CASH) {
                  dispatch(setOpenPaymentCustomAmountModal(false))
                  dispatch(setOpenCashPaymentModal(false))
                  dispatch(setCashDrawerPermission(true))
                }
                dispatch(setOpenPaymentOptionModal(false))
                dispatch(setTicketTotalAmount(currentTicket.total))
                dispatch(setIsPrintButtonExist(!isPayLater))
                dispatch(setOpenPaymentPrintModal(true))
                if (currentTicketState.openFulfillmentModal) {
                  dispatch(setOpenFulfillmentModalOpen(false))
                }
              } else {
                setIsCheck(false)
                setIsLoading(false)
              }
              if (errorMessage) {
                showErrorToast(errorMessage)
              }
            }
          )
        )
      }
    },
    [
      currentPaymentIncentive,
      currentTicketData.currentTicket,
      currentTicketState.openFulfillmentModal,
      discountType,
      dispatch,
      payableAmount,
      storeValue.data?.taxRates,
      teamMemberData?.selectedTeamMember?.teamMemberId,
      handlePrintReceipt,
    ]
  )

  const handleClickMemos = (memoIndex: number) => {
    if (currentTicketState.selectedItemIndex !== memoIndex) {
      dispatch(setInitialRender(true))
      dispatch(setSelectedMemoIndex(memoIndex))
    }
  }

  const shouldDisplayList = useMemo(() => {
    return (
      (currentTicketData?.currentTicket?.lineItems &&
        currentTicketData?.currentTicket?.lineItems?.length > 0) ||
      (currentTicketData?.currentTicket?.memos &&
        currentTicketData?.currentTicket?.memos?.length > 0) ||
      (currentTicketData?.currentTicket?.priceAdjustments &&
        currentTicketData?.currentTicket?.priceAdjustments?.length > 0)
    )
  }, [currentTicketData?.currentTicket])

  const handleClickPriceAdjustmentItem = (
    item: PriceAdjustmentData,
    index: number
  ) => {
    if (!currentTicketState.requiredVariants) {
      updateSelectedLineItem()
      handlePriceAdjustmentClick(
        index,
        item?.priceAdjustmentId ? item?.priceAdjustmentId : 0,
        item.name
      )
    }
  }
  const setPayableAmountData = useCallback(() => {
    if (
      currentTicketData?.currentTicket &&
      currentTicketData?.currentTicket?.total > 0
    ) {
      setPayableAmount(
        calculatePayableAmount(
          currentPaymentIncentive,
          currentTicketData?.currentTicket?.total || 0
        )
      )
    }
  }, [currentPaymentIncentive, currentTicketData?.currentTicket])

  const handleClickNext = useCallback(() => {
    if (isVariantRequirementFulfilled) {
      handleClickSaveEdit()
      dispatch(setOnPaymentPrintReceipt(false))
      if (currentTicketData?.currentTicket?.total === 0) {
        if (
          isP2AndP3ItemTypesExist(
            currentTicketData?.currentTicket?.lineItems || []
          )
        ) {
          dispatch(setOpenFulfillmentModalOpen(true))
        } else {
          handlePaidAmount(null)
        }
      } else {
        isP2AndP3ItemTypesExist(
          currentTicketData?.currentTicket?.lineItems || []
        )
          ? dispatch(setOpenFulfillmentModalOpen(true))
          : dispatch(setOpenPaymentOptionModal(true))
      }
      dispatch(setNoteState(null))
      dispatch(setLinItemState(null))
      dispatch(setNextButtonState(null))
    }
    setPayableAmountData()
  }, [
    isVariantRequirementFulfilled,
    currentTicketData,
    dispatch,
    handleClickSaveEdit,
    handlePaidAmount,
    setPayableAmountData,
  ])

  const handleClickSave = useCallback(() => {
    const updatedTicketDetails = updatedTicketData(
      structuredClone(currentTicketData.currentTicket) || null,
      structuredClone(currentTicketData.editTicket) || null
    )
    if (updatedTicketDetails) {
      setIsLoading(true)
      const lineItems = updatedTicketPayloadData(updatedTicketDetails)
      const hasEditedLineItems = lineItems?.some((item) => Boolean(item.action))
      const hasUpdatedDetails =
        updatedTicketDetails?.notes !== undefined ||
        updatedTicketDetails?.flagged !== undefined

      const isTicketEdited =
        hasEditedLineItems ||
        hasUpdatedDetails ||
        currentTicketData.currentTicket?.fulfillment?.isEdit
      if (isTicketEdited) {
        dispatch(
          updateCurrentTicketData(
            {
              ...updatedTicketDetails,
              lineItems,
              teamMemberId: teamMemberData?.selectedTeamMember
                ?.teamMemberId as string,
            },
            (res, ticketData: TicketDataBE[] | null, errorMessage) => {
              if (res) {
                if (ticketData) {
                  dispatch(ticketPrintSucceededAction(ticketData))
                  dispatch(setIsEditTicket(true))
                  dispatch(setIsPrintButtonExist(true))
                  dispatch(setOpenPaymentPrintModal(true))
                }
                dispatch(
                  setSelectedCustomerTicketData(
                    currentTicketData?.currentTicket?.currentTicketId || null,
                    () => {
                      setIsLoading(false)
                    }
                  )
                )
              } else {
                errorMessage && showToast(errorMessage, ToastType.ERROR)
                setIsLoading(false)
              }
            }
          )
        )
      } else {
        dispatch(deleteCurrentTicket())
        dispatch(
          setSelectedCustomerTicketData(
            currentTicketData?.currentTicket?.currentTicketId || null,
            (res) => {
              if (res) {
                originalPush(appRoutes?.tickets)
              }
              setIsLoading(false)
            }
          )
        )
      }
      dispatch(setNoteState(null))
      dispatch(setLinItemState(null))
      dispatch(setNextButtonState(null))
    }
  }, [dispatch, currentTicketData, teamMemberData, originalPush])

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.nextButtonState !== ButtonState.Clicked ||
      currentTicketData.noteState === NoteState.Focused
    ) {
      return
    }

    const { lineItemState, noteState, currentTicket } = currentTicketData

    const isLineItemComplete =
      lineItemState !== null && lineItemState === LineItemState.Blurred
    const isNoteComplete = noteState === NoteState.Blurred
    const isFlagChanged =
      currentTicket?.originalData?.flagged !== currentTicket?.flagged
    const isBothStatesNull = lineItemState === null && noteState === null

    const shouldProceed =
      isLineItemComplete || isNoteComplete || isFlagChanged || isBothStatesNull

    if (shouldProceed) {
      if (currentTicket?.isEdit) {
        handleClickSave()
      } else {
        handleClickNext()
      }
    }
  }, [currentTicketData, handleClickNext, handleClickSave])

  useEffect(() => {
    if (currentTicketData?.currentTicket?.lineItems) {
      const lineItems = currentTicketData.currentTicket.lineItems
      const itemOrder: string[] = []
      const groupedData = lineItems.reduce(
        (group: Record<string, GroupedItems>, item: LineItem) => {
          const groupKey = item.name
          if (!itemOrder.includes(groupKey)) {
            itemOrder.push(groupKey)
          }
          if (!group[groupKey]) {
            group[groupKey] = {
              name: '',
              items: [],
              totalQuantity: 0,
            }
          }
          group[groupKey].items.push(item)
          if (item?.quantity) {
            group[groupKey].totalQuantity +=
              item?.itemType === TextSale.WEIGHTED ? 1 : item?.quantity
          }

          return group
        },
        {}
      )

      const orderGroupedData: GroupedItems[] = itemOrder.map(
        (value: string) => {
          return {
            name: value,
            items: groupedData[value].items,
            totalQuantity: groupedData[value].totalQuantity,
          }
        }
      )
      setGroupItemByName(orderGroupedData)
    } else {
      setGroupItemByName([])
    }
  }, [currentTicketData, setGroupItemByName])

  useEffect(() => {
    if (currentTicketData?.currentTicket?.memos) {
      const memos = currentTicketData.currentTicket.memos
      const memoOrder: string[] = []
      const groupedMemos = memos.reduce(
        (group: Record<string, MemoGroupedItems>, memo: Memo) => {
          const groupKey = memo.name
          if (!memoOrder.includes(groupKey)) {
            memoOrder.push(groupKey)
          }
          if (!group[groupKey]) {
            group[groupKey] = {
              name: '',
              items: [],
              totalMemoCount: 0,
            }
          }
          group[groupKey].items.push(memo)
          group[groupKey].totalMemoCount += 1

          return group
        },
        {}
      )

      const orderedGroupedMemos: MemoGroupedItems[] = memoOrder.map(
        (value: string) => {
          return {
            name: value,
            items: groupedMemos[value].items,
            totalMemoCount: groupedMemos[value].totalMemoCount,
          }
        }
      )

      setGroupMemosByName(orderedGroupedMemos)
    } else {
      setGroupMemosByName([])
    }
  }, [currentTicketData, setGroupMemosByName])

  useEffect(() => {
    if (currentTicketData?.currentTicket?.priceAdjustments) {
      const priceAdjustments = currentTicketData.currentTicket.priceAdjustments
      const priceAdjustmentsOrder: string[] = []
      const groupedPriceAdjustments = priceAdjustments.reduce(
        (
          group: Record<string, PriceAdjustmentGroupedItems>,
          priceAdjustment: PriceAdjustmentData
        ) => {
          const groupKey = priceAdjustment?.name
          if (!priceAdjustmentsOrder.includes(groupKey)) {
            priceAdjustmentsOrder.push(groupKey)
          }
          if (!group[groupKey]) {
            group[groupKey] = {
              name: '',
              items: [],
              totalCount: 0,
            }
          }
          group[groupKey].items.push(priceAdjustment)
          group[groupKey].totalCount += 1

          return group
        },
        {}
      )

      const orderedGroupedPriceAdjustment: PriceAdjustmentGroupedItems[] =
        priceAdjustmentsOrder.map((value: string) => {
          return {
            name: value,
            items: groupedPriceAdjustments[value].items,
            totalCount: groupedPriceAdjustments[value].totalCount,
          }
        })

      setGroupPriceAdjustmentByName(orderedGroupedPriceAdjustment)
    }
  }, [currentTicketData, setGroupPriceAdjustmentByName])

  const handleCancelClick = (selectedItemIndex: number) => {
    if (
      currentTicketData?.currentTicket?.lineItems &&
      currentTicketData?.currentTicket?.lineItems.length > 0 &&
      selectedItemIndex !== null
    ) {
      onClickDelete()
      let itemLineData = structuredClone(
        currentTicketData?.currentTicket.lineItems
      )
      const updatedItemIndex = findItemIndexBySelectedIndex(
        currentTicketData?.currentTicket?.lineItems,
        selectedItemIndex
      )

      itemLineData.splice(updatedItemIndex, 1)

      itemLineData = itemLineData.map((value, index) => ({
        ...value,
        itemIndex: index,
      }))

      dispatch(
        addOrEditCurrentTicketData(
          updateAmounts(
            {
              ...currentTicketData?.currentTicket,
              lineItems: itemLineData,
            },
            discountType
          ),
          currentTicketData.currentTicket?.isEdit
            ? EDIT_TICKET_ACTION_TYPE.DELETE_LINE_ITEM
            : null,
          {
            lineItemIndex: updatedItemIndex,
          }
        )
      )

      dispatch(setTotalLineItem(itemLineData.length))
      dispatch(setVariantModalOpen(false))
      dispatch(setAssignedVariantData([]))
      dispatch(setIsButtonDisabled(false))
      dispatch(setRequiredVariants(null))
      dispatch(setSelectedItem(null))
      dispatch(setSelectedItemIndex(null))
      dispatch(setLinItemState(LineItemState.Blurred))
      if (currentTicketState.isItemSelected) {
        dispatch(setIsItemSelected(false))
      }
    }
  }

  const handleRemoveTicketOnClose = () => {
    setRemoveTicket(false)
  }

  const handleRemoveTicketOnSubmit = () => {
    const getPaymentDetails = updatePayloadData(
      storeValue.data?.id as number,
      [currentTicketData?.currentTicket?.currentTicketId as number],
      PaymentTypes.REMOVE_INVENTORY
    )
    if (getPaymentDetails) {
      dispatch(
        updateTicketPaymentData(getPaymentDetails, (res, data) => {
          if (res) {
            updateCustomerTickets(data)
            dispatch(clearCurrentTicket())
            dispatch(deleteCurrentTicket())
            handleRemoveTicketOnClose()
            originalPush(appRoutes?.tickets)
          }
        })
      )
    }
  }

  const handleReopenTicket = () => {
    const getPaymentDetails = updatePayloadData(
      storeValue.data?.id as number,
      [currentTicketData?.currentTicket?.currentTicketId as number],
      PaymentTypes.REOPEN
    )
    if (getPaymentDetails) {
      dispatch(
        updateTicketPaymentData(
          getPaymentDetails,
          (res, data, errorMessage) => {
            if (res) {
              updateCustomerTickets(data)
              dispatch(clearCurrentTicket())
              dispatch(deleteCurrentTicket())
              dispatch(setOpenReopenTicketModal(false))
              dispatch(setOpenMoreOptionsModalOpen(false))
              originalPush(appRoutes?.tickets)
            } else {
              showErrorToast(errorMessage)
            }
          }
        )
      )
    }
  }

  const handleVoidTicket = () => {
    const getPaymentDetails = updatePayloadDataForVoid(
      storeValue.data?.id as number,
      [currentTicketData?.currentTicket?.currentTicketId as number],
      PaymentTypes.VOID,
      reason
    )
    if (getPaymentDetails) {
      dispatch(
        updateTicketPaymentData(
          getPaymentDetails,
          (res, data: TicketDataBE[] | null, errorMessage) => {
            if (res) {
              updateCustomerTickets(data)
              dispatch(clearCurrentTicket())
              dispatch(deleteCurrentTicket())
              dispatch(setOpenVoidTicketModal(false))
              originalPush(appRoutes?.tickets)
            } else {
              showErrorToast(errorMessage)
            }
          }
        )
      )
    }
  }

  const openRespectiveModal = (modalType: string) => {
    if (!modalType) return

    switch (modalType) {
      case translation.VOID:
        dispatch(setOpenMoreOptionsModalOpen(false))
        dispatch(setOpenVoidTicketModal(true))
        break
      case translation.REMOVE_INVENTORY:
        setRemoveTicket(true)
        dispatch(setOpenMoreOptionsModalOpen(false))
        break

      case translation.REFUND:
        setRefundTicket(true)
        break

      case translation.REVERSE:
        break

      case translation.REOPEN:
        dispatch(setOpenMoreOptionsModalOpen(false))
        dispatch(setOpenReopenTicketModal(true))
        break

      default:
        console.error('Invalid modal type')
    }
  }

  const handleLeaveClick = () => {
    const { requestedRoute, requestedAction } = currentTicketState
    dispatch(deleteCurrentTicket())

    if (
      requestedAction === RequestedAction.CHANGE_USER ||
      requestedAction === RequestedAction.LOGOUT ||
      requestedAction === RequestedAction.OPEN_SETTINGS
    ) {
      handleOptionClick(requestedAction)
    }

    if (requestedAction && requestedAction !== RequestedAction.OPEN_SETTINGS) {
      dispatch(resetSelectedCustomer())
    }

    dispatch(setRequestedRoute(''))
    dispatch(setRequestedAction(null))

    if (requestedRoute) {
      setShouldBlock(false)
      originalPush(requestedRoute)
    }

    dispatch(setOpenLeaveConfirmationModal(false))
  }

  const handleLeaveOnClose = () => {
    cancelNavigation()
    dispatch(setRequestedRoute(''))
    dispatch(setOpenLeaveConfirmationModal(false))
    dispatch(setRequestedAction(null))
  }

  useEffect(() => {
    if (
      !currentTicketData ||
      currentTicketData.flagButtonState !== ButtonState.Clicked
    )
      return

    if (currentTicketData.noteState !== NoteState.Focused) {
      dispatch(
        addOrEditCurrentTicketData(
          {
            ...currentTicketData?.currentTicket,
            flagged: !currentTicketData?.currentTicket?.flagged,
          },
          null,
          null
        )
      )
      dispatch(setNoteState(null))
      dispatch(setFlagButtonState(null))
    }
  }, [handleItemClick, selectedItemData, currentTicketData, dispatch])

  const shouldShowRequiredVariantError = useMemo(() => {
    return (
      currentTicketState.selectedItemIndex !== null && checkIsSaveButtonEnabled
    )
  }, [currentTicketState.selectedItemIndex, checkIsSaveButtonEnabled])

  const getSaveButtonText = (): string => {
    if (hasUnresolvedVariants) {
      return `${translation.REQUIRED} (${currentTicketState.requiredVariants})`
    }
    return translation.SAVE
  }

  const handleSaveOrRequiredClick = () => {
    if (hasUnresolvedVariants) {
      onScrollToRequired()
    } else {
      if (!checkIsSaveButtonEnabled) {
        dispatch(setNextButtonState(ButtonState.Clicked))
      } else {
        handleClickSave()
      }
    }
  }

  return (
    <Card variant="bgCardTicketsP1" className={`py-20 col-6 `}>
      <Flex
        as="div"
        sx={{ height: '100%', overflow: 'hidden', flexDirection: 'column' }}
      >
        <Box
          as="div"
          className="px-20"
          variant={
            storeValue.data && storeValue.data?.posProductType > 1
              ? 'styles.ticketContainer'
              : ''
          }
        >
          <Text
            variant="Primary18Demi111"
            sx={{ fontWeight: '600', lineHeight: '20px' }}
            as="p"
          >
            {`${translation.TICKET} ${currentTicketData?.currentTicket?.currentTicketId ? formatTicketNumber(currentTicketData?.currentTicket?.currentTicketId) : ''}`}
          </Text>
          {storeValue.data && storeValue.data?.posProductType > 1 && (
            <Box
              as="div"
              sx={{ display: 'flex', gap: '20px', cursor: 'pointer' }}
            >
              <Image
                width={20}
                height={20}
                src={disableHand}
                alt="disableHand"
              />
              <Image
                width={20}
                height={20}
                src={
                  currentTicketData?.currentTicket?.flagged
                    ? selectedFlag
                    : disableFlag
                }
                alt="disableFlag"
                onClick={() => {
                  dispatch(setFlagButtonState(ButtonState.Clicked))
                }}
              />
            </Box>
          )}
        </Box>
        <Box
          className={`px-20 ${currentTicketData?.currentTicket?.fulfillment?.date ? 'pt-14' : ''}`}
        >
          {currentTicketData?.currentTicket?.fulfillment?.date &&
            typeof currentTicketData?.currentTicket?.fulfillment?.time ===
              'number' && (
              <Text variant="NotoSans14Medium125">
                {formatTimeLabel(
                  translation.DUE,
                  currentTicketData?.currentTicket?.fulfillment?.date,
                  currentTicketData?.currentTicket?.fulfillment?.time
                )}
              </Text>
            )}
        </Box>
        <Divider sx={{ my: '10px', color: '#000' }} className="mx-20" />
        {shouldDisplayList ? (
          <Box
            variant="styles.scrollBothEdgesPadding"
            className={`default-state-height-ticket-box ${currentTicketState.selectedItemIndex ? 'ticket-scroll' : ''}`}
            sx={{
              overflowY: 'auto',
              bg: 'white',
              width: '100%',
              scrollbarGutter: 'stable both-edges',
            }}
            ref={itemLineContainerRef}
          >
            {
              <AnimatePresence>
                {groupItemByName &&
                  groupItemByName?.map((value: GroupedItems, index: number) => (
                    <div key={`item-${value.name}-${index}`}>
                      <div className="row align-items-center mb-2 col-12">
                        <div className="col-1">
                          <Text variant="Secondary16Demi12">
                            {value.totalQuantity}
                          </Text>
                        </div>
                        <Box
                          as="div"
                          className="col-sm-10 col-xl-11"
                          sx={{
                            maxWidth: '40ch',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          <Text variant="Secondary16Demi12">{value.name}</Text>
                        </Box>
                      </div>

                      {value.items.map((item) => {
                        return (
                          <CurrentTicketItemLine
                            clearEmptySublines={clearEmptySublines}
                            index={item.itemIndex as number}
                            handleClickSaveEdit={handleClickSaveEdit}
                            variantRef={variantRef}
                            itemWeightModalRef={itemWeightModalRef}
                            serviceModalRef={serviceModalRef}
                            item={item}
                            key={`itemdata-${item.itemIndex}`}
                            isSelected={
                              currentTicketState.selectedItemIndex ===
                              item.itemIndex
                            }
                            onClick={() => {
                              executeIfEditable(() => {
                                dispatch(setLinItemState(LineItemState.Clicked))
                                setSelectedItemData({
                                  itemIndex: item.itemIndex as number,
                                  itemId: item.itemId,
                                  name: item.name,
                                  quantity: item.quantity as number,
                                })
                              })
                            }}
                            selectedIndex={
                              currentTicketState.selectedItemIndex ===
                              item.itemIndex
                                ? item.itemIndex
                                : null
                            }
                            toggleVariantModal={(value: boolean) =>
                              dispatch(setVariantModalOpen(value))
                            }
                            itemSelected={(value: boolean) =>
                              dispatch(setIsItemSelected(value))
                            }
                            isItemSelected={currentTicketState?.isItemSelected}
                            currentTicket={
                              currentTicketData.currentTicket || {}
                            }
                            setSelectedItem={(
                              value: selectedItemData | null
                            ) => {
                              executeIfEditable(() =>
                                dispatch(setSelectedItem(value))
                              )
                            }}
                            setSelectedItemIndex={(value: number | null) => {
                              executeIfEditable(() =>
                                dispatch(setSelectedItemIndex(value))
                              )
                            }}
                            onClickDelete={onClickDelete}
                            isEditTicket={
                              currentTicketData.currentTicket?.isEdit || false
                            }
                          />
                        )
                      })}
                      {value.totalQuantity != 0 && (
                        <Divider className="current-ticket-bottom-divider" />
                      )}
                    </div>
                  ))}
              </AnimatePresence>
            }

            {groupPriceAdjustmentByName &&
              groupPriceAdjustmentByName?.map(
                (value: PriceAdjustmentGroupedItems, index: number) => (
                  <div key={`priceAdjustment-${value?.name}-${index}`}>
                    <div className="row align-items-center mb-2 col-12">
                      <div className="col-1">
                        <Text variant="Secondary16Demi12">
                          {value.totalCount}
                        </Text>
                      </div>
                      <Box
                        as="div"
                        className="col-sm-10 col-xl-11"
                        sx={{
                          maxWidth: '40ch',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        <Text variant="Secondary16Demi12">{value.name}</Text>
                      </Box>
                    </div>

                    {value.items.map((item: PriceAdjustmentData) => {
                      return (
                        <CurrentTicketPriceAdjustment
                          priceAdjustment={item}
                          key={`priceAdjustment-${item?.priceAdjustmentIndex}-${item.priceAdjustmentIndex}`}
                          isSelected={
                            currentTicketState.selectedPriceAdjustmentIndex ===
                            item?.priceAdjustmentIndex
                          }
                          onClick={() => {
                            handleClickPriceAdjustmentItem(
                              item,
                              Number(item?.priceAdjustmentIndex)
                            )
                          }}
                          selectedIndex={
                            currentTicketState.selectedPriceAdjustmentIndex ===
                            item?.priceAdjustmentIndex
                              ? item?.priceAdjustmentIndex
                              : null
                          }
                          itemSelected={(value: boolean) =>
                            dispatch(setIsItemSelected(value))
                          }
                          isItemSelected={currentTicketState?.isItemSelected}
                          currentTicket={currentTicketData.currentTicket || {}}
                          setSelectedPriceAdjustment={(
                            value: SelectedPriceAdjustmentData | null
                          ) => dispatch(setSelectedPriceAdjustment(value))}
                          setSelectedPriceAdjustmentIndex={(
                            value: number | null
                          ) => dispatch(setSelectedPriceAdjustmentIndex(value))}
                        />
                      )
                    })}
                    {value.totalCount != 0 && (
                      <Divider className="current-ticket-bottom-divider" />
                    )}
                  </div>
                )
              )}

            {groupMemosByName &&
              groupMemosByName?.map(
                (value: MemoGroupedItems, index: number) => (
                  <div key={`memos-${value.name}-${index}`}>
                    <div className="row align-items-center mb-2 col-12">
                      <div className="col-1">
                        <Text variant="Secondary16Demi12">
                          {value.totalMemoCount}
                        </Text>
                      </div>
                      <Box
                        as="div"
                        className="col-sm-10 col-xl-11"
                        sx={{
                          maxWidth: '40ch',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        <Text variant="Secondary16Demi12">{value.name}</Text>
                      </Box>
                    </div>
                    {value.items.map((memo: Memo) => {
                      return (
                        <CurrentTicketMemo
                          memo={memo}
                          key={`memos-${memo.name}-${memo.memoIndex}`}
                          isSelected={
                            currentTicketState.selectedMemoIndex ===
                            memo?.memoIndex
                          }
                          onClick={() => {
                            handleClickMemos(Number(memo.memoIndex))
                          }}
                          selectedIndex={
                            currentTicketState.selectedMemoIndex ===
                            memo?.memoIndex
                              ? memo?.memoIndex
                              : null
                          }
                          itemSelected={(value: boolean) =>
                            dispatch(setIsItemSelected(value))
                          }
                          isItemSelected={currentTicketState?.isItemSelected}
                          currentTicket={currentTicketData.currentTicket || {}}
                          setSelectedMemoIndex={(value: number | null) =>
                            dispatch(setSelectedMemoIndex(value))
                          }
                        />
                      )
                    })}
                    {value.totalMemoCount != 0 && (
                      <Divider className="current-ticket-bottom-divider" />
                    )}
                  </div>
                )
              )}
          </Box>
        ) : (
          <Flex
            sx={{
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
            }}
          >
            <Text
              variant="Primary14Medium88"
              sx={{
                fontFamily: 'notoSans',
                color: 'muted',
                textAlign: 'center',
              }}
            >
              {translation.ADD_ITEM_TO_START}
            </Text>
          </Flex>
        )}
      </Flex>
      <div className="px-20 mt-10">
        {currentTicketData?.currentTicket?.isEdit &&
          shouldShowRequiredVariantError && (
            <Box sx={{ mb: 2, textAlign: 'center' }}>
              <Text
                variant="Primary14Medium88"
                sx={{
                  color: '#FF0000',
                  fontFamily: 'notoSans',
                  fontSize: '14px',
                  fontWeight: '500',
                }}
              >
                {translation.SELECT_REQUIRED_VARIANTS}
              </Text>
            </Box>
          )}
        {currentTicketData?.currentTicket?.isEdit ? (
          <Flex sx={{ gap: '10px' }}>
            <ThemeButton
              className={`print-edit-btn-ticket-box text-center ${isTicketClosed ? 'w-100' : ''}`}
              icon={moreButton}
              variant={
                isTicketClosedAndUnpaid
                  ? 'printEditTextIconBtnTicketBox'
                  : 'printEditIconBtnTicketBox'
              }
              text={shouldShowMoreText ? translation.MORE : ''}
              onClick={() => dispatch(setOpenMoreOptionsModalOpen(true))}
              disabled={
                ticketContainsOnlySalesAndOtherItems && isTicketClosedAndUnpaid
              }
            />
            {isTicketOpen && (
              <ThemeButton
                className="print-edit-btn-ticket-box text-center"
                icon={calendarEditIcon}
                variant={'printEditIconBtnTicketBox'}
                onClick={() => {
                  if (
                    actualTicketData?.fulfillment &&
                    Object.keys(actualTicketData?.fulfillment).length > 0
                  ) {
                    dispatch(setOpenFulfillmentModalOpen(true))
                  }
                }}
              />
            )}
            <ThemeButton
              className={`print-edit-btn-ticket-box ${isTicketClosed ? 'w-100' : 'w-50'}`}
              text={getSaveButtonText()}
              icon={currentTicketState.requiredVariants ? null : saveTicketIcon}
              sx={{
                minWidth: '80px',
                py: [2, 2, 2, 2, 2, 2, 3],
              }}
              type="button"
              id={SAVE_BUTTON_ID}
              onClick={handleSaveOrRequiredClick}
            />
          </Flex>
        ) : (
          <ThemeButton
            className="cta-button"
            disabled={checkIsButtonDisabled()}
            icon={
              currentTicketState.requiredVariants
                ? null
                : checkIsButtonDisabled()
                  ? chargeCheckoutDisabled
                  : chargeCheckout
            }
            sx={{ width: '100%', py: [2, 2, 2, 2, 2, 2, 3] }}
            text={getButtonText()}
            id={NEXT_BUTTON_ID}
            onClick={() => {
              if (!checkIsSaveButtonEnabled) {
                dispatch(setNextButtonState(ButtonState.Clicked))
              }
              if (
                currentTicketState.selectedItemIndex !== null &&
                currentTicketData.currentTicket?.lineItems &&
                currentTicketData.currentTicket?.lineItems.length > 0 &&
                currentTicketState.requiredVariants !== null &&
                currentTicketState.requiredVariants > 0
              ) {
                const updateItemQuantityByIndex = findItemIndexBySelectedIndex(
                  currentTicketData.currentTicket?.lineItems,
                  currentTicketState.selectedItemIndex as number
                )
                const lineItems = structuredClone(
                  currentTicketData.currentTicket?.lineItems
                )
                lineItems[updateItemQuantityByIndex].isQuantityModalOpen = false
                lineItems[updateItemQuantityByIndex].quantity =
                  currentTicketState.quantityForItem

                onScrollToRequired()
                scrollToBottom(updateItemQuantityByIndex)
                dispatch(setQuantityModalOpen(false))

                dispatch(
                  addOrEditCurrentTicketData(
                    updateAmounts(
                      {
                        ...currentTicketData?.currentTicket,
                        lineItems,
                      },
                      discountType
                    ),
                    null,
                    null
                  )
                )
              }
              if (
                currentTicketState.selectedItemIndex !== null &&
                currentTicketData.currentTicket?.lineItems &&
                currentTicketData.currentTicket?.lineItems.length > 0
              ) {
                const updateItemQuantityByIndex = findItemIndexBySelectedIndex(
                  currentTicketData.currentTicket?.lineItems,
                  currentTicketState.selectedItemIndex as number
                )
                scrollToBottom(updateItemQuantityByIndex)
              }
            }}
          />
        )}
      </div>

      <PaymentModalWrapper
        amount={currentTicketData?.currentTicket?.total || 0}
        amountCash={totalAmountNeedsToPay}
        onBack={
          isP2AndP3ItemTypesExist(
            currentTicketData?.currentTicket?.lineItems || []
          )
            ? () => {
                dispatch(setOpenFulfillmentModalOpen(true))
                dispatch(setOpenPaymentOptionModal(false))
              }
            : undefined
        }
        loading={loading}
        currentPaymentIncentive={currentPaymentIncentive}
        isCheck={isCheck}
        paidValue={paidValue || 0}
        paymentType={paymentType as PaymentMethodType}
        totalAmount={currentTicketData?.currentTicket?.total || 0}
        updateIsCheck={(value: boolean) => {
          setIsCheck(value)
        }}
        updatePayableAmount={(value?: number) => {
          setPayableAmount(value)
        }}
        updatePaidValue={(value?: number) => {
          setPaidValue(value)
        }}
        cardPaymentAmount={totalAmountNeedsToPay}
        handlePaidAmount={handlePaidAmount}
        payableAmount={payableAmount}
        amountCard={totalAmountNeedsToPay}
        mode={PosLayoutMode.POS}
        isFulfillmentModalOpen={isP2AndP3ItemTypesExist(
          currentTicketData?.currentTicket?.lineItems || []
        )}
      />

      {currentTicketState?.openFulfillmentModal && (
        <FulfillmentModal
          isOpen={currentTicketState?.openFulfillmentModal}
          onClose={() => {
            dispatch(setOpenFulfillmentModalOpen(false))
            dispatch(
              addOrEditCurrentTicketData(
                {
                  ...currentTicketData?.currentTicket,
                  fulfillment: currentTicketData?.currentTicket?.isEdit
                    ? currentTicketData?.currentTicket?.fulfillment
                    : undefined,
                },
                null,
                null
              )
            )
          }}
          title={translation.FULFILLMENT_MODAL}
          modalContainer="fulfillment-modal"
          handleSave={handlePaidAmount}
          showCloseIcon
          handlePrepay={(fulfillment: Fulfillment) => {
            dispatch(setOpenFulfillmentModalOpen(false))
            dispatch(setOpenPaymentOptionModal(true))
            dispatch(
              addOrEditCurrentTicketData(
                {
                  ...currentTicketData?.currentTicket,
                  ...{ fulfillment },
                },
                null,
                null
              )
            )
          }}
          loading={loading}
          actualTicketData={actualTicketData}
        />
      )}

      {currentTicketState?.openMoreOptionsModal &&
        !(removeTicket || refundTicket) && (
          <MoreOptionsModal
            isOpen={currentTicketState?.openMoreOptionsModal}
            onClose={() => dispatch(setOpenMoreOptionsModalOpen(false))}
            title={translation.MORE_OPTIONS}
            modalContainer={'more-options-modal'}
            ticketData={actualTicketData}
            isLoading={false}
            openRespectiveModal={openRespectiveModal}
          />
        )}

      {currentTicketState?.openVoidTicketModal && (
        <VoidTicketModal
          title={translation.VOID_TICKET}
          isOpen={currentTicketState?.openVoidTicketModal}
          onClose={() => {
            dispatch(setOpenVoidTicketModal(false))
            dispatch(setOpenMoreOptionsModalOpen(true))
          }}
          modalContainer="void-ticket-modal"
          setReason={setReason}
        />
      )}

      {currentTicketState?.openVoidConfirmationModal && reason && (
        <CommonConfirmationModal
          isOpen={currentTicketState?.openVoidConfirmationModal}
          onClose={() => {
            setOpenVoidConfirmationModal(false)
            dispatch(setOpenVoidTicketModal(true))
          }}
          title={translation?.VOID_TICKET_CONFIRMATION}
          description={translation.VOID_TICKET_DESCRIPTION}
          isLoading={false}
          submitButtonText={translation.VOID}
          onClickSubmit={handleVoidTicket}
        />
      )}

      {currentTicketState?.openReopenTicketModal && (
        <CommonConfirmationModal
          isOpen={currentTicketState?.openReopenTicketModal}
          onClose={() => {
            dispatch(setOpenReopenTicketModal(false))
            dispatch(setOpenMoreOptionsModalOpen(true))
          }}
          title={translation.REOPEN_TICKET}
          description={translation.REOPEN_TICKET_DESCRIPTION}
          isLoading={currentTicketData?.loading}
          submitButtonText={translation.REOPEN}
          onClickSubmit={handleReopenTicket}
        />
      )}

      {removeTicket && (
        <CommonConfirmationModal
          isOpen={removeTicket}
          onClose={handleRemoveTicketOnClose}
          title={translation?.REMOVE_TICKET_CONFIRMATION}
          description={translation?.REMOVE_TICKET_DESCRIPTION}
          isLoading={currentTicketData?.loading}
          submitButtonText={translation.REMOVE}
          onClickSubmit={handleRemoveTicketOnSubmit}
        />
      )}

      {currentTicketData?.currentTicket?.flagged && (
        <Box
          className={`flag-class ${storeValue?.data?.posLayout === PosLayout.TICKET_POS ? 'flag-left' : 'flag-right'}`}
        >
          <Text color="errorElementLight" variant="Primary80Demi90">
            {FLAG}
          </Text>
        </Box>
      )}

      {currentTicketState?.isWeightModalOpen && (
        <ItemWeightModal
          isOpen={currentTicketState?.isWeightModalOpen}
          onClose={(selectFrom, selectedItemIndex) => {
            if (selectFrom === ItemSelection.SALE) {
              handleCancelClick(selectedItemIndex)
            }
            dispatch(setWeightModalOpen(false))
          }}
          title={translation.ITEM_WEIGHT}
          modalContainer="change-user"
          itemWeightModalRef={itemWeightModalRef}
        />
      )}

      {currentTicketState?.isServiceModalOpen && (
        <ServiceModal
          serviceModalRef={serviceModalRef}
          isOpen={currentTicketState?.isServiceModalOpen}
          modalContainer={''}
          title={''}
        />
      )}
      {refundTicket && (
        <RefundTicketModal
          receiptPaidBy="Cash"
          actualTicketData={actualTicketData}
          isOpen={refundTicket}
          onClose={() => {
            setRefundTicket(false)
          }}
          title={translation?.REFUND_TICKET}
          ticketId={currentTicketData?.currentTicket?.currentTicketId as number}
        />
      )}
      {currentTicketState.openLeaveConfirmationModal && (
        <ConfirmLeaveModal
          isOpen={currentTicketState.openLeaveConfirmationModal}
          onClose={handleLeaveOnClose}
          onLeaveClick={handleLeaveClick}
          title={translation.UNSAVED_CHANGES}
          isLoading={loading}
        />
      )}
      {currentTicketData?.currentTicket?.isEdit &&
        !currentTicketState.openLeaveConfirmationModal &&
        !currentTicketState?.openFulfillmentModal && (
          <Spinner visible={loading} />
        )}
    </Card>
  )
}

export default CurrentTicket
