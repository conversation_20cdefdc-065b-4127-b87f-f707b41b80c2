import {
  updatePrinterData,
  updateSelectedPrinterData,
} from '@/store/actions/printers.action'
import {
  DeviceStatus,
  DropdownItems,
  EnabledDeviceState,
  MqttMessage,
  PrinterData,
  PrinterDataProps,
} from '@/types/module/printersModule'
import { translation } from '@/utils/translation'
import Image from 'next/image'
import React, { useEffect, useRef, useState } from 'react'
import { useDispatch } from 'react-redux'
import { Paragraph, Spinner } from 'theme-ui'
import checkIcon from '@/../public/images/check-circle-icon.svg'
import printerIcon from '@/../public/images/manage-printer-icon.svg'
import printerDisabled from '@/../public/images/printer-disabled-scan.svg'
import DropdownItem from '@/components/Hardware/printer/dropdownItems'
import { publishToMultipleTopic } from '@/utils/useMqtt'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'

const MqttEnabledPrinters: React.FC<PrinterDataProps> = ({
  printer,
  topicForPrinterStatus,
  setIsDropdownOpen,
  setIsPrinterEdit,
  setOpenModal,
  setPrinterEditData,
  removePrintersHandler,
}) => {
  const dispatch = useDispatch()
  const printers = useSelector(
    (state: MainStoreType) => state?.printersData.data
  )
  const [message, setMessage] = useState<MqttMessage | null>(null)
  const printerRef = useRef(false)
  const [isConnected, setIsConnected] = useState<boolean | null>(null)
  const [isRefresh, setIsRefresh] = useState<number>(0)
  const isValid = localStorage.getItem(translation.CLIENT_CONNECTED)
  const [deviceStatus, setDeviceStatus] = useState<
    DeviceStatus | string | null
  >(DeviceStatus.Pending)

  const dropdownItems: DropdownItems[] = [
    {
      label: EnabledDeviceState.EDIT,
      onClick: (printer) => {
        setIsPrinterEdit(true)
        setOpenModal(true)
        setPrinterEditData(printer)
      },
    },
    {
      label: EnabledDeviceState.REFRESH,
      onClick: () => {
        setIsConnected(null)
        setIsRefresh(isRefresh + 1)
        setDeviceStatus(DeviceStatus.Pending)
        forPrintStatus(topicForPrinterStatus)
      },
    },
    ...(deviceStatus !== DeviceStatus.DeviceNotFound
      ? isConnected !== null
        ? deviceStatus !== DeviceStatus.DeviceNotFound &&
          deviceStatus !== DeviceStatus.Connected &&
          deviceStatus !== DeviceStatus.Pending
          ? [
              {
                label: EnabledDeviceState.CONNECT,
                onClick: () => {
                  toggleStatus(EnabledDeviceState.CONNECT, printers, printer)
                },
              },
            ]
          : [
              {
                label: EnabledDeviceState.DISCONNECT,
                onClick: () => {
                  toggleStatus(EnabledDeviceState.DISCONNECT, printers, printer)
                },
              },
            ]
        : []
      : []),
    {
      label: EnabledDeviceState.DISABLE,
      onClick: () => {
        dispatch(
          updatePrinterData({ id: printer.id, enabled: false }, () => {})
        )
      },
    },
    {
      label: EnabledDeviceState.DELETE,
      onClick: (printer) => removePrintersHandler(printer),
    },
  ]

  const forPrintStatus = (topic: string) => {
    publishToMultipleTopic(
      topic,
      JSON.stringify({
        title: translation.REQUEST_CLIENT_STATUS,
      }),
      (message) => {
        if (message) {
          const parsedMessage = message && JSON.parse(message)
          setMessage(parsedMessage)
        }
      }
    )
  }

  useEffect(() => {
    if (isValid && JSON.parse(isValid) && printer?.macAddress) {
      if (topicForPrinterStatus) {
        if (!printerRef.current) {
          forPrintStatus(topicForPrinterStatus)
          printerRef.current = true
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topicForPrinterStatus, isValid])

  useEffect(() => {
    let initialStatus = false

    if (message) {
      const getStatusCode = message?.statusCode.startsWith('2')

      setIsConnected(getStatusCode as boolean)
      const isSelected = printer.isSelected && getStatusCode
      const deviceStatus = isSelected
        ? DeviceStatus.Connected
        : DeviceStatus.NotConnected
      setDeviceStatus(deviceStatus)

      initialStatus = getStatusCode ?? false
    }

    if (!initialStatus) {
      const timeoutId = setTimeout(() => {
        setIsConnected(initialStatus)
        setDeviceStatus(DeviceStatus.DeviceNotFound)
      }, 5000)

      return () => clearTimeout(timeoutId)
    } else {
      return () => {}
    }
  }, [message, isRefresh, printer])

  const toggleStatus = async (
    status: EnabledDeviceState,
    printers: PrinterData[] | null | undefined,
    targetPrinter: PrinterData
  ) => {
    const userPreference = JSON.parse(
      localStorage.getItem(translation.USER_PREFERENCE) || '{}'
    )

    const updatedPrinters = printers?.map((device) => {
      if (status === EnabledDeviceState.CONNECT && !targetPrinter.isSelected) {
        if (device.id === targetPrinter.id) {
          device.isSelected = !targetPrinter.isSelected
        }
      } else if (
        status === EnabledDeviceState.DISCONNECT &&
        targetPrinter.isSelected
      ) {
        if (device.id === targetPrinter.id) {
          device.isSelected = !targetPrinter.isSelected
        }
      }
      return device
    })

    const connectedPrinters: PrinterData[] | [] =
      updatedPrinters?.filter((device) => device.isSelected) || []
    await dispatch(updateSelectedPrinterData(connectedPrinters))
    userPreference.printers = connectedPrinters.map((printer: PrinterData) => ({
      macAddress: printer.macAddress,
      name: printer.name,
      id: printer.id,
    }))

    localStorage.setItem(
      translation.USER_PREFERENCE,
      JSON.stringify(userPreference)
    )
  }

  return (
    <div className="row mb-20 align-items-center pt-1">
      <div className="col-11 position-relative">
        <div
          className="row g-0 align-items-center printer-list-border px-30 py-20"
          style={
            isConnected !== null
              ? !isConnected
                ? { backgroundColor: '#f1f1f1' }
                : {}
              : {}
          }
        >
          <div
            className="col-11"
            style={
              isConnected !== null ? (!isConnected ? { opacity: 0.5 } : {}) : {}
            }
          >
            <div className="row g-0 align-items-center">
              <div className="col-1 text-center">
                <Image
                  height={36}
                  width={36}
                  alt="printer-icon"
                  src={
                    isConnected !== null
                      ? isConnected
                        ? printerIcon
                        : printerDisabled
                      : printerIcon
                  }
                />
              </div>
              <div className="col-10 ml-20">
                <Paragraph variant="Secondary18Demi20">
                  {printer?.name}
                </Paragraph>
                <Paragraph variant="Secondary18Medium20" pt={'10px'}>
                  {deviceStatus}
                </Paragraph>
              </div>
            </div>
          </div>
          <div className="col-1 text-end">
            <DropdownItem
              dropdownItems={dropdownItems}
              setIsDropdownOpen={setIsDropdownOpen}
              printer={printer}
            />
          </div>
        </div>
      </div>
      <div className="col-1">
        {isConnected !== null ? (
          deviceStatus === DeviceStatus.Connected && (
            <Image height={30} width={30} alt="check-icon" src={checkIcon} />
          )
        ) : (
          <Spinner size={35} />
        )}
      </div>
    </div>
  )
}

export default MqttEnabledPrinters
