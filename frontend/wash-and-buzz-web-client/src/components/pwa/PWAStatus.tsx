'use client'

import { usePWA } from '@/hooks/usePWA'

const PWAStatus = () => {
  const {
    isOnline,
    isInstalled,
    canInstall,
    isStandalone,
    installApp,
    shareApp,
    requestNotificationPermission,
  } = usePWA()

  const handleInstall = async () => {
    const success = await installApp()
    if (success) {
      console.log('App installed successfully')
    }
  }

  const handleShare = async () => {
    const success = await shareApp()
    if (!success) {
      // Fallback to copying URL
      try {
        await navigator.clipboard.writeText(window.location.origin)
        alert('App URL copied to clipboard!')
      } catch (error) {
        console.error('Failed to copy URL:', error)
      }
    }
  }

  const handleNotificationPermission = async () => {
    const granted = await requestNotificationPermission()
    if (granted) {
      console.log('Notification permission granted')
    } else {
      console.log('Notification permission denied')
    }
  }

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">PWA Status</h5>
      </div>
      <div className="card-body">
        <div className="row g-3">
          {/* Online Status */}
          <div className="col-md-6">
            <div className="d-flex align-items-center">
              <div
                className={`badge ${isOnline ? 'bg-success' : 'bg-danger'} me-2`}
              >
                {isOnline ? '●' : '●'}
              </div>
              <span>{isOnline ? 'Online' : 'Offline'}</span>
            </div>
          </div>

          {/* Installation Status */}
          <div className="col-md-6">
            <div className="d-flex align-items-center">
              <div
                className={`badge ${isInstalled ? 'bg-success' : 'bg-secondary'} me-2`}
              >
                {isInstalled ? '●' : '●'}
              </div>
              <span>{isInstalled ? 'Installed' : 'Not Installed'}</span>
            </div>
          </div>

          {/* Standalone Mode */}
          <div className="col-md-6">
            <div className="d-flex align-items-center">
              <div
                className={`badge ${isStandalone ? 'bg-success' : 'bg-secondary'} me-2`}
              >
                {isStandalone ? '●' : '●'}
              </div>
              <span>{isStandalone ? 'Standalone Mode' : 'Browser Mode'}</span>
            </div>
          </div>

          {/* Notification Permission */}
          <div className="col-md-6">
            <div className="d-flex align-items-center">
              <div
                className={`badge ${
                  typeof window !== 'undefined' &&
                  'Notification' in window &&
                  Notification.permission === 'granted'
                    ? 'bg-success'
                    : 'bg-secondary'
                } me-2`}
              >
                ●
              </div>
              <span>
                {typeof window !== 'undefined' && 'Notification' in window
                  ? Notification.permission === 'granted'
                    ? 'Notifications Enabled'
                    : 'Notifications Disabled'
                  : 'Notifications Not Supported'}
              </span>
            </div>
          </div>
        </div>

        <hr />

        <div className="row g-2">
          {/* Install Button */}
          {canInstall && !isInstalled && (
            <div className="col-md-4">
              <button
                className="btn btn-primary btn-sm w-100"
                onClick={handleInstall}
              >
                Install App
              </button>
            </div>
          )}

          {/* Share Button */}
          <div className="col-md-4">
            <button
              className="btn btn-outline-primary btn-sm w-100"
              onClick={handleShare}
            >
              Share App
            </button>
          </div>

          {/* Notification Permission Button */}
          {typeof window !== 'undefined' &&
            'Notification' in window &&
            Notification.permission !== 'granted' && (
              <div className="col-md-4">
                <button
                  className="btn btn-outline-secondary btn-sm w-100"
                  onClick={handleNotificationPermission}
                >
                  Enable Notifications
                </button>
              </div>
            )}
        </div>

        {/* PWA Features List */}
        <div className="mt-3">
          <small className="text-muted">
            <strong>PWA Features:</strong>
            <ul className="list-unstyled mt-1 mb-0">
              <li>✓ Offline functionality</li>
              <li>✓ Install to home screen</li>
              <li>✓ Background sync</li>
              <li>✓ Push notifications</li>
              <li>✓ Responsive design</li>
              <li>✓ Fast loading</li>
            </ul>
          </small>
        </div>
      </div>
    </div>
  )
}

export default PWAStatus
