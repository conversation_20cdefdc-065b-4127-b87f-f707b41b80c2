'use client'

import { useEffect, useState } from 'react'

const NetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(true)
  const [showOfflineMessage, setShowOfflineMessage] = useState(false)

  useEffect(() => {
    // Set initial online status
    setIsOnline(navigator.onLine)

    const handleOnline = () => {
      setIsOnline(true)
      setShowOfflineMessage(false)
    }

    const handleOffline = () => {
      setIsOnline(false)
      setShowOfflineMessage(true)
    }

    // Add event listeners
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Auto-hide offline message after coming back online
  useEffect(() => {
    if (isOnline && showOfflineMessage) {
      const timer = setTimeout(() => {
        setShowOfflineMessage(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [isOnline, showOfflineMessage])

  if (!showOfflineMessage && isOnline) {
    return null
  }

  return (
    <div
      className={`position-fixed top-0 start-0 end-0 p-2 text-center text-white ${
        isOnline ? 'bg-success' : 'bg-warning text-dark'
      }`}
      style={{ zIndex: 9999 }}
    >
      <div className="d-flex align-items-center justify-content-center">
        <div className="me-2">
          {isOnline ? (
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                fill="currentColor"
              />
            </svg>
          ) : (
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24.24 8l-2.12-2.12C19.32 3.08 15.91 1.5 12 1.5S4.68 3.08 1.88 5.88L0 8l12 12 12-12zM12 4c3.07 0 5.91 1.23 7.94 3.26L12 15.2 4.06 7.26C6.09 5.23 8.93 4 12 4z"
                fill="currentColor"
              />
              <path
                d="M3.27 3.27L20.73 20.73"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          )}
        </div>
        <span className="small fw-bold">
          {isOnline
            ? 'Back online! Data will sync automatically.'
            : 'You are offline. Some features may be limited.'}
        </span>
      </div>
    </div>
  )
}

export default NetworkStatus
