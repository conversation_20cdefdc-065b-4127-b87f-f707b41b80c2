'use client'

import { useEffect, useState } from 'react'

interface NotificationState {
  permission: NotificationPermission
  isSupported: boolean
  isSubscribed: boolean
}

const PushNotifications = () => {
  const [state, setState] = useState<NotificationState>({
    permission: 'default',
    isSupported: false,
    isSubscribed: false,
  })

  useEffect(() => {
    // Check if notifications are supported
    const isSupported = 'Notification' in window && 'serviceWorker' in navigator

    setState((prev) => ({
      ...prev,
      isSupported,
      permission: isSupported ? Notification.permission : 'denied',
    }))

    // Check if already subscribed
    if (isSupported && 'PushManager' in window) {
      checkSubscription()
    }
  }, [])

  const checkSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      setState((prev) => ({ ...prev, isSubscribed: !!subscription }))
    } catch (error) {
      console.error('Error checking push subscription:', error)
    }
  }

  const requestPermission = async () => {
    if (!state.isSupported) return false

    try {
      const permission = await Notification.requestPermission()
      setState((prev) => ({ ...prev, permission }))
      return permission === 'granted'
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      return false
    }
  }

  const subscribeToPush = async () => {
    if (!state.isSupported || state.permission !== 'granted') {
      const granted = await requestPermission()
      if (!granted) return false
    }

    try {
      const registration = await navigator.serviceWorker.ready

      // You would need to generate VAPID keys for production
      // For now, we'll use a placeholder
      const vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY_HERE'

      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: vapidPublicKey,
      })

      // Send subscription to your server
      await sendSubscriptionToServer(subscription)

      setState((prev) => ({ ...prev, isSubscribed: true }))
      return true
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
      return false
    }
  }

  const unsubscribeFromPush = async () => {
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()

      if (subscription) {
        await subscription.unsubscribe()
        // Remove subscription from your server
        await removeSubscriptionFromServer(subscription)
        setState((prev) => ({ ...prev, isSubscribed: false }))
      }
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
    }
  }

  const sendSubscriptionToServer = async (subscription: PushSubscription) => {
    // This would send the subscription to your backend
    // For now, we'll just log it
    console.log('Subscription to send to server:', JSON.stringify(subscription))

    // Example API call:
    // await fetch('/api/push/subscribe', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(subscription),
    // })
  }

  const removeSubscriptionFromServer = async (
    subscription: PushSubscription
  ) => {
    // This would remove the subscription from your backend
    console.log(
      'Subscription to remove from server:',
      JSON.stringify(subscription)
    )

    // Example API call:
    // await fetch('/api/push/unsubscribe', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(subscription),
    // })
  }

  const sendTestNotification = () => {
    if (state.permission === 'granted') {
      new Notification('WashAndBuzz Test', {
        body: 'This is a test notification from WashAndBuzz!',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        tag: 'test-notification',
        requireInteraction: false,
      })
    }
  }

  if (!state.isSupported) {
    return (
      <div className="alert alert-warning">
        <strong>Push notifications are not supported</strong> in this browser.
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <h6 className="card-title mb-0">Push Notifications</h6>
      </div>
      <div className="card-body">
        <div className="mb-3">
          <div className="d-flex align-items-center mb-2">
            <div
              className={`badge ${
                state.permission === 'granted'
                  ? 'bg-success'
                  : state.permission === 'denied'
                    ? 'bg-danger'
                    : 'bg-warning'
              } me-2`}
            >
              ●
            </div>
            <span>
              Permission:{' '}
              {state.permission === 'granted'
                ? 'Granted'
                : state.permission === 'denied'
                  ? 'Denied'
                  : 'Not requested'}
            </span>
          </div>

          <div className="d-flex align-items-center">
            <div
              className={`badge ${state.isSubscribed ? 'bg-success' : 'bg-secondary'} me-2`}
            >
              ●
            </div>
            <span>
              Subscription: {state.isSubscribed ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        <div className="d-flex gap-2 flex-wrap">
          {state.permission !== 'granted' && (
            <button
              className="btn btn-primary btn-sm"
              onClick={requestPermission}
            >
              Request Permission
            </button>
          )}

          {state.permission === 'granted' && !state.isSubscribed && (
            <button
              className="btn btn-success btn-sm"
              onClick={subscribeToPush}
            >
              Subscribe to Push
            </button>
          )}

          {state.isSubscribed && (
            <button
              className="btn btn-outline-danger btn-sm"
              onClick={unsubscribeFromPush}
            >
              Unsubscribe
            </button>
          )}

          {state.permission === 'granted' && (
            <button
              className="btn btn-outline-primary btn-sm"
              onClick={sendTestNotification}
            >
              Test Notification
            </button>
          )}
        </div>

        <div className="mt-3">
          <small className="text-muted">
            <strong>Notification Types:</strong>
            <ul className="list-unstyled mt-1 mb-0">
              <li>• Order ready for pickup</li>
              <li>• Payment confirmations</li>
              <li>• Delivery updates</li>
              <li>• Special promotions</li>
            </ul>
          </small>
        </div>
      </div>
    </div>
  )
}

export default PushNotifications
