'use client'
import siteLogo from '@/../public/images/bubbles-white.png'
import changeUser from '@/../public/images/change-user.svg'
import homeIcon from '@/../public/images/home-icon.svg'
import logOut from '@/../public/images/logout.svg'
import profileImage from '@/../public/images/profile.svg'
import settings from '@/../public/images/settings.svg'
import changeStore from '@/../public/images/store-icon.svg'
import { ChangeStoreModal } from '@/components/core/PopupModals/ChangeStore'
import { LogOutModal } from '@/components/core/PopupModals/LogOutModal'
import SelectCustomerDropDown from '@/components/SelectCustomerDropDown/SelectCustomerDropDown'
import { useUserAccountActions } from '@/hooks/useUserAccountActions'
import { setIsIdeal } from '@/store/actions/common.action'
import {
  setOpenLeaveConfirmationModal,
  setRequestedAction,
  setRequestedRoute,
} from '@/store/actions/currentTicketState.action'
import { resetSelectedCustomer } from '@/store/actions/customer.action'
import { getItemData } from '@/store/actions/item.actions'
import { RequestedAction } from '@/types/module/ticketNdSalesModule'
import { PrimaryTopbarProps } from '@/types/module/topbarModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { ComponentAccess } from '@/utils/componentAccess'
import {
  ADMIN_ACCESS_SETTINGS,
  ADMIN_CHANGE_STORE,
  IS_TEAM_MEMBER_SELECTED,
  TRUE,
} from '@/utils/constant'
import { hasPermission } from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import { checkProductHasAccess } from '@/utils/strings'
import { translation } from '@/utils/translation'
import Cookies from 'js-cookie'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box, Divider, Flex, Text } from 'theme-ui'

export const PrimaryTopbar: React.FC<PrimaryTopbarProps> = ({
  storeName = 'ABC Cleaners',
}) => {
  const { handleOptionClick, handleOptionEditClick } = useUserAccountActions()
  const dispatch = useDispatch()
  const storeValue = useSelector((state: MainStoreType) => state.storeData)
  const commonData = useSelector((state: MainStoreType) => state.commonData)
  const effectExecuted = useRef(false)
  const permissionData = useSelector(
    (state: MainStoreType) => state.permissionsData
  )
  const currentTicketState = useSelector(
    (state: MainStoreType) => state.ticketsData
  )
  const teamMemberData = useSelector(
    (state: MainStoreType) => state.teamMemberData
  )

  const router = useRouter()

  const handleUserClick = () => {
    if (currentTicketState.currentTicket?.isEdit) {
      handleOptionEditClick(RequestedAction.CHANGE_USER)
      return
    }
    handleOptionClick(RequestedAction.CHANGE_USER)
    router.push(appRoutes.homePage)
  }

  useEffect(() => {
    if (effectExecuted.current) return

    effectExecuted.current = true

    const isTeamMemberSelected = Cookies.get(IS_TEAM_MEMBER_SELECTED)

    if (isTeamMemberSelected === TRUE && commonData.data.isIdeal) {
      dispatch(setIsIdeal(false))
    }
  }, [dispatch, commonData])

  const [openStorePopupModal, setOpenStorePopupModal] = useState(false)
  const [openLogoutModal, setOpenLogoutModal] = useState(false)
  // eslint-disable-next-line no-unused-vars
  const [, setSelectedStore] = useState(storeName)
  const [dropdownItems, setDropdownItems] = useState([
    {
      icon: changeUser,
      label: 'Change User',
      onClick: handleUserClick,
    },
  ])
  const handleStoreChange = (newStoreName: string) => {
    setSelectedStore(newStoreName)
    setOpenStorePopupModal(false)
  }

  const updateDropdownItems = useCallback(() => {
    if (
      teamMemberData.selectedTeamMember?.positionId &&
      permissionData.data &&
      permissionData.data.length > 0
    ) {
      const items = [
        {
          icon: changeUser,
          label: 'Change User',
          onClick: handleUserClick,
        },
      ]

      // Check for Change Store permission
      const hasStorePermission = hasPermission(
        permissionData?.data || [],
        teamMemberData.selectedTeamMember?.positionId || '',
        ADMIN_CHANGE_STORE
      )
      // Check for Settings permission
      const hasSettingsPermission = hasPermission(
        permissionData?.data || [],
        teamMemberData.selectedTeamMember?.positionId || '',
        ADMIN_ACCESS_SETTINGS
      )

      // Add Change Store item if the user has permission
      if (hasStorePermission) {
        items.push({
          icon: changeStore,
          label: 'Change Store',
          onClick: () => {
            if (currentTicketState.currentTicket?.isEdit) {
              handleOptionEditClick(RequestedAction.CHANGE_STORE)
              return
            }
            handleOptionClick(RequestedAction.CHANGE_STORE)
          },
        })
      }

      // Add Settings item if the user has permission
      if (hasSettingsPermission) {
        items.push({
          icon: settings,
          label: 'Settings',
          onClick: () => {
            if (currentTicketState.currentTicket?.isEdit) {
              handleOptionEditClick(RequestedAction.OPEN_SETTINGS)
              return
            }
            handleOptionClick(RequestedAction.OPEN_SETTINGS)
          },
        })
      }

      // Add Logout item
      items.push({
        icon: logOut,
        label: 'Log Out',
        onClick: () => {
          if (currentTicketState.currentTicket?.isEdit) {
            handleOptionEditClick(RequestedAction.LOGOUT)
            return
          }
          setOpenLogoutModal(true)
        },
      })

      setDropdownItems(items)
    }
    // eslint-disable-next-line
  }, [permissionData, teamMemberData, router])

  useEffect(() => {
    dispatch(getItemData())
    updateDropdownItems()
  }, [dispatch, updateDropdownItems])

  return (
    <React.Fragment>
      <nav className={`navbar navbar-web navbar-expand-xl`}>
        <Box p={['20px']} as={'div'} className="container-fluid">
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Image src={siteLogo} height={40} width={40} alt="navbarLogo" />

            {checkProductHasAccess(
              ComponentAccess.headerSearchBar,
              storeValue.data?.posProductType as number
            ) && (
              <div className="pl-20">
                <SelectCustomerDropDown />
              </div>
            )}
          </Box>
          <Flex sx={{ alignItems: 'center' }}>
            {storeValue?.data?.posProductType &&
              storeValue?.data?.posProductType > 1 && (
                <div>
                  <button
                    onClick={() => {
                      if (!currentTicketState.currentTicket?.isEdit) {
                        dispatch(resetSelectedCustomer())
                        router.push(appRoutes?.homePage)
                      } else {
                        dispatch(setRequestedRoute(appRoutes.homePage))
                        dispatch(setOpenLeaveConfirmationModal(true))
                        dispatch(
                          setRequestedAction(
                            RequestedAction.RESET_CUSTOMER_DATA
                          )
                        )
                      }
                    }}
                    className={`home-icon-topbar`}
                  >
                    <Image width={20} height={20} src={homeIcon} alt="img" />
                    <Text
                      variant="Primary16Regular20"
                      sx={{ mt: '8px', color: '#fff' }}
                    >
                      {translation.HOME}
                    </Text>
                  </button>
                </div>
              )}
            <Flex
              sx={{
                flexDirection: 'column',
                alignItems: 'center',
                position: 'relative',
                cursor: 'pointer',
                p: ['8px 23px'],
                borderRadius: 8,
              }}
              id="dropdownMenuButton"
              data-bs-toggle="dropdown"
              className="user-navbar"
            >
              <Image
                className="profile-img"
                height={20}
                width={20}
                src={profileImage}
                alt="user"
              />
              <Text
                sx={{
                  color: 'white',
                  mt: '10px',
                  maxWidth: '10ch',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer',
                }}
                variant="Primary18Medium20"
              >
                {teamMemberData.selectedTeamMember?.name}
              </Text>
              <div className="dropdown">
                <Text
                  as="ul"
                  className="dropdown-menu dropdown-web p-0 dropdown-menu-end"
                  aria-labelledby="dropdownMenuButton"
                >
                  {dropdownItems?.map((item, index: number) => (
                    <React.Fragment key={index}>
                      <Flex
                        sx={{ alignItems: 'center', p: '20px' }}
                        className="dropdown-item-container"
                        onClick={item?.onClick}
                      >
                        {item?.icon && (
                          <Image
                            height={20}
                            width={20}
                            src={item?.icon}
                            alt={item?.label}
                          />
                        )}
                        <Text
                          as="li"
                          sx={{ pl: '10px' }}
                          className="dropdown-item"
                          variant="Primary16Regular20"
                        >
                          {item?.label}
                        </Text>
                      </Flex>
                      {index !== dropdownItems.length - 1 && <Divider m={0} />}
                    </React.Fragment>
                  ))}
                </Text>
              </div>
            </Flex>
          </Flex>
        </Box>
      </nav>
      <ChangeStoreModal
        isOpen={openStorePopupModal}
        onClose={() => setOpenStorePopupModal(false)}
        closeIcon=""
        title={translation?.CHANGE_STORE}
        setStoreName={handleStoreChange}
        containerClass="change-Store"
        selectedStore={storeValue?.data?.name ? storeValue?.data?.name : ''}
      />
      <LogOutModal
        isOpen={openLogoutModal}
        onClose={() => setOpenLogoutModal(false)}
        closeIcon={''}
        title={translation?.CONFIRM_LOGOUT}
        modalContainer="change-Store"
      />
    </React.Fragment>
  )
}
