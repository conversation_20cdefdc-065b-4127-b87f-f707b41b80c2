'use client'

import PWAStatus from '@/components/pwa/PWAStatus'
import PushNotifications from '@/components/pwa/PushNotifications'

const PWADemoPage = () => {
  return (
    <div className="container-fluid py-4">
      <div className="row">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h1 className="h3 mb-0">PWA Demo & Testing</h1>
            <div className="badge bg-primary">Progressive Web App</div>
          </div>
        </div>
      </div>

      <div className="row g-4">
        {/* PWA Status Card */}
        <div className="col-lg-6">
          <PWAStatus />
        </div>

        {/* Push Notifications Card */}
        <div className="col-lg-6">
          <PushNotifications />
        </div>

        {/* PWA Features Overview */}
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">PWA Implementation Status</h5>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-6">
                  <h6 className="text-success">✅ Implemented Features</h6>
                  <ul className="list-unstyled">
                    <li>✅ Service Worker Registration</li>
                    <li>✅ Web App Manifest</li>
                    <li>✅ Offline Support</li>
                    <li>✅ Install Prompt</li>
                    <li>✅ Network Status Detection</li>
                    <li>✅ Caching Strategies</li>
                    <li>✅ PWA Meta Tags</li>
                    <li>✅ Responsive Design</li>
                  </ul>
                </div>
                <div className="col-md-6">
                  <h6 className="text-warning">🔄 In Progress</h6>
                  <ul className="list-unstyled">
                    <li>🔄 Push Notifications (Setup)</li>
                    <li>🔄 Background Sync</li>
                    <li>🔄 Proper PWA Icons</li>
                    <li>🔄 Performance Optimization</li>
                  </ul>

                  <h6 className="text-info mt-3">📋 Planned Features</h6>
                  <ul className="list-unstyled">
                    <li>📋 Web Share API</li>
                    <li>📋 Badge API</li>
                    <li>📋 File System Access</li>
                    <li>📋 App Store Distribution</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Testing Instructions */}
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">PWA Testing Instructions</h5>
            </div>
            <div className="card-body">
              <div className="row g-4">
                <div className="col-md-6">
                  <h6>🔧 Development Testing</h6>
                  <ol>
                    <li>
                      Build for production: <code>npm run build</code>
                    </li>
                    <li>
                      Start production server: <code>npm start</code>
                    </li>
                    <li>Open Chrome DevTools → Application tab</li>
                    <li>Check Service Workers and Manifest sections</li>
                  </ol>
                </div>
                <div className="col-md-6">
                  <h6>📱 Mobile Testing</h6>
                  <ol>
                    <li>Open in Chrome/Edge on mobile</li>
                    <li>Look for "Add to Home Screen" prompt</li>
                    <li>Install the app</li>
                    <li>Test offline functionality</li>
                  </ol>
                </div>
                <div className="col-md-6">
                  <h6>🚀 Lighthouse Audit</h6>
                  <ol>
                    <li>Open Chrome DevTools → Lighthouse</li>
                    <li>Select "Progressive Web App" category</li>
                    <li>Run audit</li>
                    <li>Check PWA score and recommendations</li>
                  </ol>
                </div>
                <div className="col-md-6">
                  <h6>🌐 Offline Testing</h6>
                  <ol>
                    <li>Open Network tab in DevTools</li>
                    <li>Set throttling to "Offline"</li>
                    <li>Refresh the page</li>
                    <li>Verify offline page appears</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* PWA Resources */}
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">PWA Resources & Tools</h5>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-3">
                  <h6>🛠️ Development Tools</h6>
                  <ul className="list-unstyled">
                    <li>
                      <a
                        href="https://www.pwabuilder.com/"
                        target="_blank"
                        rel="noopener"
                      >
                        PWA Builder
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://developers.google.com/web/tools/lighthouse"
                        target="_blank"
                        rel="noopener"
                      >
                        Lighthouse
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://developers.google.com/web/tools/workbox"
                        target="_blank"
                        rel="noopener"
                      >
                        Workbox
                      </a>
                    </li>
                  </ul>
                </div>
                <div className="col-md-3">
                  <h6>📊 Testing Tools</h6>
                  <ul className="list-unstyled">
                    <li>
                      <a
                        href="https://www.webpagetest.org/"
                        target="_blank"
                        rel="noopener"
                      >
                        WebPageTest
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://web.dev/measure/"
                        target="_blank"
                        rel="noopener"
                      >
                        Web.dev Measure
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://realfavicongenerator.net/"
                        target="_blank"
                        rel="noopener"
                      >
                        Favicon Generator
                      </a>
                    </li>
                  </ul>
                </div>
                <div className="col-md-3">
                  <h6>📚 Documentation</h6>
                  <ul className="list-unstyled">
                    <li>
                      <a
                        href="https://web.dev/progressive-web-apps/"
                        target="_blank"
                        rel="noopener"
                      >
                        Web.dev PWA
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps"
                        target="_blank"
                        rel="noopener"
                      >
                        MDN PWA Guide
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://nextjs.org/docs/basic-features/progressive-web-app"
                        target="_blank"
                        rel="noopener"
                      >
                        Next.js PWA
                      </a>
                    </li>
                  </ul>
                </div>
                <div className="col-md-3">
                  <h6>🎨 Icon Tools</h6>
                  <ul className="list-unstyled">
                    <li>
                      <a
                        href="https://www.pwabuilder.com/imageGenerator"
                        target="_blank"
                        rel="noopener"
                      >
                        PWA Icon Generator
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://maskable.app/"
                        target="_blank"
                        rel="noopener"
                      >
                        Maskable.app
                      </a>
                    </li>
                    <li>
                      <a
                        href="https://appsco.pe/developer/splash-screens"
                        target="_blank"
                        rel="noopener"
                      >
                        Splash Screen Generator
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PWADemoPage
