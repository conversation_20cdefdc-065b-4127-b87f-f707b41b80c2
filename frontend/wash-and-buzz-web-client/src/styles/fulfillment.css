.nav-tabs-fulfillments .tabs {
  display: flex;
  gap: 20px;
  border-bottom: 2px solid #eaeaea;
  width: 90%;
}

.nav-tabs-fulfillments .tab {
  padding: 10px 8px 3px 8px;
  border: none;
  background: none;
  cursor: pointer;
  color: #a6a6a6;
  border-bottom: 3px solid transparent;
  margin-bottom: -2px;
}

.nav-tabs-fulfillments .tab.active {
  color: #303030;
  border-bottom: 3px solid #00c6b7;
  margin-bottom: -2px;
}

.red-input-box {
  background-color: #ffdcd9 !important;
  border: 1px solid #ffdcd9 !important;
  border-radius: 4px !important;
  box-shadow: none !important;
  text-align: center !important;
  height: 45px !important;
  width: 40px !important;
}

.fulfillment-button {
  cursor: pointer;
}

.fulfillment-button.selected {
  background-color: #00c6b7;
  color: white;
  border-color: #00c6b7;
}

.calendar-wrapper {
  width: 76.5% !important;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background: #fbfbfb;
}

.time-range-wrapper {
  width: 22% !important;
  height: 331px;
  overflow-y: auto;
}

.time-range-wrapper-cleaning-weight {
  width: 22% !important;
  height: 202px;
  overflow-y: auto;
}

.gaping-4 {
  row-gap: 8px !important;
}

.cleaning-type-wrapper {
  width: 31% !important;
  display: flex;
  flex-wrap: wrap;
}

.cleaning-type-box {
  width: 49% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-color: #f2f2f2;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  padding: 10px 14px !important;
  height: 51px !important;
}

.diagonal-card {
  position: relative;
  background-color: #f2f2f2;
  border-radius: 4px;
  overflow: hidden;
  height: 51px !important;
  padding: 10px 14px;
}

.diagonal-line {
  position: absolute;
  width: 91%;
  height: 1px;
  background-color: black;
  top: 51%;
  left: 4%;
  transform: rotate(-23deg);
}

.diagonal-line-value-box {
  position: absolute;
  width: 100%;
  height: 1px;
  background-color: black;
  top: 51%;
  right: 2%;
  transform: rotate(-48deg);
}

.diagonal-card-content {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  width: 100%;
}

.top-text {
  position: absolute;
  top: 3px;
  left: 13px;
}

.bottom-text {
  position: absolute;
  bottom: 4px;
  right: 22px;
}

.top-text-value {
  position: absolute;
  top: 3px;
  left: 6px;
}

.bottom-text-value {
  position: absolute;
  bottom: 0px;
  right: 5px;
}

.work-load-wrapper {
  width: 67% !important;
}

.work-load-card {
  width: 51px;
  height: 51px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #eaeaea;
  background-color: #ffffff;
  border-radius: 4px;
  padding: 10px 14px;
}

.work-load-content {
  display: flex;
  flex-direction: column;
}

.work-load-row {
  display: flex;
  gap: 12px;
}

.work-load-card.highlighted {
  background-color: #ffdcd9;
  border: 1px solid transparent;
}

.rush-wrapper {
  width: 30% !important;
}

.weekdays-row-wrapper {
  width: 67% !important;
}

.right-arrow-button {
  position: absolute;
  right: -12px;
}

.left-arrow-button {
  position: absolute;
  left: -29px;
}

.day-date-custom-box {
  cursor: pointer;
  width: 51px;
  height: 94px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  border: 1px solid #00c6b7;
  background-color: transparent;
  color: black;
  font-weight: bold;
  position: relative;
  border-radius: 6px;
  border: 1px solid #00c6b7;
  text-align: center;
}

.day-date-custom-box-selected {
  background-color: #00c6b7;
  color: white;
}

.today-indicator-dot {
  position: absolute;
  bottom: 7px;
  left: 50%;
  transform: translateX(-50%);
  width: 0.28rem;
  height: 0.28rem;
  border-radius: 50%;
  background-color: black;
}

.today-indicator-dot-selected {
  background-color: white;
}

.rush-icon-default {
  filter: invert(0%);
  /* Black icon */
}

.rush-icon-selected {
  filter: brightness(0) invert(1);
  /* White icon */
}

.rush-selected {
  background-color: #00c6b7 !important;
  color: #fff !important;
}

.rush-default {
  background-color: #fff !important;
}

.selected-time {
  background-color: #00c6b7 !important;
  color: #ffffff !important;
}
