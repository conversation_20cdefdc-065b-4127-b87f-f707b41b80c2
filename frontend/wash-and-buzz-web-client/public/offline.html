<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WashAndBuzz - Offline</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        .feature {
            margin: 0.5rem 0;
            opacity: 0.8;
        }
        .feature::before {
            content: "✓ ";
            color: #4ade80;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🌐</div>
        <h1>You're Offline</h1>
        <p>Don't worry! WashAndBuzz works offline too. You can still access your recent data and continue working.</p>

        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>

        <div class="features">
            <div class="feature">View recent tickets and orders</div>
            <div class="feature">Access customer information</div>
            <div class="feature">Create new tickets (will sync when online)</div>
            <div class="feature">View reports and analytics</div>
        </div>
    </div>

    <script>
        // Check for network connectivity
        function checkOnline() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }

        // Listen for online event
        window.addEventListener('online', checkOnline);

        // Periodically check connectivity
        setInterval(checkOnline, 5000);
    </script>
</body>
</html>