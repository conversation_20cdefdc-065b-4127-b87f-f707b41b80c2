{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "version": "0.2", "language": "en", "words": ["formik", "dexie", "<PERSON><PERSON>", "popins", "<PERSON><PERSON>", "noto", "indexdb", "roboto", "topbar", "variants", "classname", "HHMM", "ampm", "idbinit", "camelcase", "Inputsx", "Zipcode", "sidemenu", "sidenav", "subsidenav", "themui", "subline", "<PERSON><PERSON>", "menulink", "borderless", "upcharge", "uuidv", "Ssemessage", "socekt", "mulitiple", "backendmessage", "firstname", "lastname", "multipay", "notoSans", "UPCHARGES", "Upcharges", "upsert", "Upsert", "sublines", "startdate", "Imask", "enddate", "assignedcategory", "ITEMDATA", "CLOUDPRNT", "cloudprnt", "reduxjs", "CREDITCARD", "itemtype", "ITEMTYPE", "<PERSON><PERSON>", "tokenform", "Bayside", "cputil", "starprnt", "Newtonsoft", "Micronics", "runtimepack", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "mscorlib", "netstandard", "createdump", "libclrjit", "libcoreclr", "libcoreclrtraceptprovider", "libdbgshim", "libhostfxr", "libhostpolicy", "libmscordaccore", "libmscordbi", "netcoreapp", "netcore", "nupkg", "sixlabors", "imagesharp", "starmicronics", "codepages", "linuxmint", "sles", "Toastify", "toastify", "mytoken", "flagg", "ccnumfield", "cccardlabel", "ccexpiryfieldmonth", "ccexpiryfieldyear", "ccexpirymonth", "ccexpirylabel", "disallowinput", "ccexpiryyear", "cccvvfield", "cccvvlabel", "useexpiry", "usecvv", "cardinputmaxlength", "cardnumbernumericonly", "formatinput", "maskfirsttwo", "enhancedresponse", "invalidcreditcardevent", "invalidexpiryevent", "invalidinputevent", "sendcardtypingevent", "tokenizewheninactive", "fullmobilekeyboard", "cardlabel", "cardtitle", "expirylabel", "cvvlabel", "placeholderyear", "placeholdercvv", "placeholdermonth", "inactivityto", "customerid", "ISVALIDCLIENT", "Dropoff", "NEXTAUTH", "codegen", "HHMMA", "COLOR_NAVYBLUE", "dryrun", "jspdf", "autotable", "halign", "ISSUBSCRIBE", "<PERSON><PERSON>", "Ven<PERSON>", "overscan", "dropup"], "caseSensitive": false, "ignorePaths": ["node_modules", "build", "dist", "public", "coverage", "tsconfig.tsbuildinfo", "cspell.json", "cspell-dictionary.json", "*.css", "*.md", "app-config.json", "jest.config.ts", "test-results", "playwright-report", "blob-report", "playwright", "allure-report", "allure-results"]}